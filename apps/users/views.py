from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils import timezone


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_today_usage(request):
    """
    获取用户今日使用情况
    """
    user = request.user
    today = timezone.now().date()
    
    today_count = user.get_today_generation_count()
    remaining = user.get_remaining_generations_today()
    
    return Response({
        'date': today.isoformat(),
        'total_generations': today_count,
        'daily_limit': user.daily_generation_limit,
        'remaining': remaining,
        'can_generate': user.can_generate_today(),
        'is_admin': user.is_superuser
    })
