from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils import timezone
from .serializers import UserSerializer, UpdateUserSerializer
import logging

logger = logging.getLogger(__name__)


@api_view(['GET', 'PUT'])
@permission_classes([IsAuthenticated])
def user_profile(request):
    """
    获取或更新用户信息
    GET: 获取当前用户信息
    PUT: 更新当前用户信息
    """
    user = request.user

    if request.method == 'GET':
        try:
            serializer = UserSerializer(user)
            logger.info(f"Retrieved profile for user {user.id}")
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error retrieving user profile {user.id}: {e}")
            return Response(
                {'error': 'Failed to retrieve user profile'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    elif request.method == 'PUT':
        try:
            serializer = UpdateUserSerializer(user, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                logger.info(f"Updated profile for user {user.id}")

                # 返回更新后的完整用户信息
                response_serializer = UserSerializer(user)
                return Response(response_serializer.data)
            else:
                logger.warning(f"Invalid data for user profile update {user.id}: {serializer.errors}")
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error updating user profile {user.id}: {e}")
            return Response(
                {'error': 'Failed to update user profile'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_today_usage(request):
    """
    获取用户今日使用情况
    """
    user = request.user
    today = timezone.now().date()

    today_count = user.get_today_generation_count()
    remaining = user.get_remaining_generations_today()

    return Response({
        'date': today.isoformat(),
        'total_generations': today_count,
        'daily_limit': user.daily_generation_limit,
        'remaining': remaining,
        'can_generate': user.can_generate_today(),
        'is_admin': user.is_superuser
    })
