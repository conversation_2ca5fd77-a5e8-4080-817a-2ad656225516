from rest_framework import serializers
from django.contrib.auth import get_user_model

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    """用户信息序列化器 - 用于获取用户信息"""
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'nickname', 'avatar_url', 
            'provider', 'daily_generation_limit',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'email', 'provider', 'daily_generation_limit',
            'created_at', 'updated_at'
        ]


class UpdateUserSerializer(serializers.ModelSerializer):
    """用户信息更新序列化器 - 用于更新用户信息"""
    
    class Meta:
        model = User
        fields = ['nickname', 'avatar_url']
        
    def validate_nickname(self, value):
        """验证昵称"""
        if value and len(value.strip()) == 0:
            raise serializers.ValidationError("昵称不能为空字符串")
        if value and len(value) > 100:
            raise serializers.ValidationError("昵称长度不能超过100个字符")
        return value.strip() if value else value
    
    def validate_avatar_url(self, value):
        """验证头像URL"""
        if value and not value.startswith(('http://', 'https://')):
            raise serializers.ValidationError("头像URL必须是有效的HTTP/HTTPS链接")
        return value
