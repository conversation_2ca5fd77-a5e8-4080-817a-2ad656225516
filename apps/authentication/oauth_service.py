import requests
import jwt
from google.auth.transport import requests as google_requests
from google.oauth2 import id_token
from django.conf import settings
# 注意：现在主要使用PyJWT(jwt)库，python-jose作为备用
import logging
import time

logger = logging.getLogger(__name__)


class GoogleOAuthService:
    @staticmethod
    def verify_id_token(id_token_str):
        try:
            idinfo = id_token.verify_oauth2_token(
                id_token_str, 
                google_requests.Request(), 
                settings.GOOGLE_OAUTH_CLIENT_ID
            )
            
            if idinfo['iss'] not in ['accounts.google.com', 'https://accounts.google.com']:
                raise ValueError('Wrong issuer.')
            
            return {
                'provider_user_id': idinfo['sub'],
                'email': idinfo['email'],
                'nickname': idinfo.get('name', ''),
                'avatar_url': idinfo.get('picture', ''),
                'email_verified': idinfo.get('email_verified', False)
            }
        except ValueError as e:
            logger.error(f"Google OAuth verification failed: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error during Google OAuth verification: {e}")
            return None


class AppleOAuthService:
    APPLE_JWKS_URL = 'https://appleid.apple.com/auth/keys'
    
    @staticmethod
    def _jwk_to_pem(jwk):
        """备用方案：将JWK转换为PEM格式的公钥"""
        try:
            from cryptography.hazmat.primitives import serialization
            from cryptography.hazmat.primitives.asymmetric import rsa
            import base64
            
            # 解码JWK中的n和e参数
            n = int.from_bytes(
                base64.urlsafe_b64decode(jwk['n'] + '=='), 
                byteorder='big'
            )
            e = int.from_bytes(
                base64.urlsafe_b64decode(jwk['e'] + '=='), 
                byteorder='big'
            )
            
            # 创建RSA公钥
            public_numbers = rsa.RSAPublicNumbers(e, n)
            public_key = public_numbers.public_key()
            
            # 转换为PEM格式
            pem = public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
            return pem
        except Exception as e:
            logger.error(f"Failed to convert JWK to PEM: {e}")
            return None
    
    @staticmethod
    def get_apple_public_keys():
        try:
            response = requests.get(AppleOAuthService.APPLE_JWKS_URL)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"Failed to fetch Apple public keys: {e}")
            return None
    
    @staticmethod
    def verify_id_token(id_token_str):
        try:
            # 使用PyJWT解码header获取kid
            header = jwt.get_unverified_header(id_token_str)
            kid = header['kid']
            
            keys = AppleOAuthService.get_apple_public_keys()
            if not keys:
                return None
            
            # 找到匹配的公钥
            signing_key = None
            for key in keys['keys']:
                if key['kid'] == kid:
                    signing_key = key
                    break
            
            if not signing_key:
                logger.error("Unable to find matching public key for Apple ID token")
                return None
            
            # 使用PyJWT解码，它能自动处理JWK
            try:
                # 先尝试使用PyJWT的JWK支持
                from jwt.algorithms import RSAAlgorithm
                public_key = RSAAlgorithm.from_jwk(signing_key)
                
                payload = jwt.decode(
                    id_token_str,
                    public_key,
                    algorithms=['RS256'],
                    audience=settings.APPLE_OAUTH_CLIENT_ID,
                    issuer='https://appleid.apple.com'
                )
            except Exception as jwt_error:
                # 如果PyJWT失败，使用备用方案
                logger.warning(f"PyJWT failed, trying fallback method: {jwt_error}")
                public_key_pem = AppleOAuthService._jwk_to_pem(signing_key)
                if not public_key_pem:
                    return None
                
                payload = jwt.decode(
                    id_token_str,
                    public_key_pem,
                    algorithms=['RS256'],
                    audience=settings.APPLE_OAUTH_CLIENT_ID,
                    issuer='https://appleid.apple.com'
                )
            
            return {
                'provider_user_id': payload['sub'],
                'email': payload.get('email', ''),
                'nickname': payload.get('name', ''),
                'avatar_url': '',
                'email_verified': payload.get('email_verified', False)
            }
            
        except jwt.InvalidTokenError as e:
            logger.error(f"Apple OAuth JWT verification failed: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error during Apple OAuth verification: {e}")
            return None