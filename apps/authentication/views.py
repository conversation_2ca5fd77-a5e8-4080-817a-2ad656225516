from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from .oauth_service import GoogleOAuthService, AppleOAuthService
from .authentication import generate_jwt_token
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


@api_view(['POST'])
@permission_classes([AllowAny])
def google_login(request):
    id_token = request.data.get('id_token')
    
    if not id_token:
        return Response(
            {'error': 'id_token is required'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    user_info = GoogleOAuthService.verify_id_token(id_token)
    
    if not user_info:
        return Response(
            {'error': 'Invalid Google ID token'}, 
            status=status.HTTP_401_UNAUTHORIZED
        )
    
    if not user_info.get('email_verified', False):
        return Response(
            {'error': 'Email not verified'}, 
            status=status.HTTP_401_UNAUTHORIZED
        )
    
    try:
        user, created = User.objects.get_or_create(
            provider='google',
            provider_user_id=user_info['provider_user_id'],
            defaults={
                'email': user_info['email'],
                'nickname': user_info['nickname'],
                'avatar_url': user_info['avatar_url'],
            }
        )
        
        if not created:
            user.nickname = user_info['nickname']
            user.avatar_url = user_info['avatar_url']
            user.save()
        
        access_token = generate_jwt_token(user)
        
        return Response({
            'access_token': access_token,
            'user': {
                'id': str(user.id),
                'email': user.email,
                'nickname': user.nickname,
                'avatar_url': user.avatar_url
            }
        })
    
    except Exception as e:
        logger.error(f"Error during Google login: {e}")
        return Response(
            {'error': 'Internal server error'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([AllowAny])
def apple_login(request):
    id_token = request.data.get('id_token')
    
    if not id_token:
        return Response(
            {'error': 'id_token is required'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    user_info = AppleOAuthService.verify_id_token(id_token)
    
    if not user_info:
        return Response(
            {'error': 'Invalid Apple ID token'}, 
            status=status.HTTP_401_UNAUTHORIZED
        )
    
    try:
        user, created = User.objects.get_or_create(
            provider='apple',
            provider_user_id=user_info['provider_user_id'],
            defaults={
                'email': user_info['email'],
                'nickname': user_info['nickname'],
                'avatar_url': user_info['avatar_url'],
            }
        )
        
        if not created:
            user.nickname = user_info['nickname']
            user.avatar_url = user_info['avatar_url']
            user.save()
        
        access_token = generate_jwt_token(user)
        
        return Response({
            'access_token': access_token,
            'user': {
                'id': str(user.id),
                'email': user.email,
                'nickname': user.nickname,
                'avatar_url': user.avatar_url
            }
        })
    
    except Exception as e:
        logger.error(f"Error during Apple login: {e}")
        return Response(
            {'error': 'Internal server error'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
