from django.test import TestCase
from django.contrib.auth import get_user_model
from apps.authentication.authentication import generate_jwt_token, JWTAuthentication
from rest_framework.test import APITestCase
from rest_framework import status

User = get_user_model()


class AuthenticationTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            provider='google',
            provider_user_id='test123'
        )
    
    def test_jwt_token_generation(self):
        """测试 JWT token 生成"""
        token = generate_jwt_token(self.user)
        self.assertIsNotNone(token)
        self.assertIsInstance(token, str)
    
    def test_jwt_authentication(self):
        """测试 JWT 认证"""
        token = generate_jwt_token(self.user)
        
        # 模拟请求
        from django.test import RequestFactory
        from rest_framework.request import Request
        
        factory = RequestFactory()
        request = factory.get('/', HTTP_AUTHORIZATION=f'Bearer {token}')
        request = Request(request)
        
        auth = JWTAuthentication()
        user, token = auth.authenticate(request)
        
        self.assertEqual(user, self.user)


class APITestCase(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            provider='google',
            provider_user_id='api123'
        )
        self.token = generate_jwt_token(self.user)
    
    def test_upload_presigned_url_unauthorized(self):
        """测试未认证的上传 URL 请求"""
        response = self.client.post('/api/v1/upload/presigned-url', {
            'file_name': 'test.jpg',
            'file_type': 'image/jpeg'
        })
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_google_login_invalid_token(self):
        """测试无效的 Google token"""
        response = self.client.post('/api/v1/login/google', {
            'id_token': 'invalid_token'
        })
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
