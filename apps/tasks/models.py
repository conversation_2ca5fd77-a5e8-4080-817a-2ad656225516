import uuid
from django.db import models
from django.contrib.auth import get_user_model


User = get_user_model()


class CharacterGenerationTask(models.Model):
    """
    角色生成任务表 - 记录每一次用户发起的角色生成请求，与Celery任务一一对应
    """
    
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('PROCESSING', 'Processing'), 
        ('SUCCESS', 'Success'),
        ('FAILED', 'Failed'),
    ]
    
    id = models.UUIDField(
        primary_key=True, 
        default=uuid.uuid4, 
        editable=False,
        help_text="任务ID，与 Celery 的 task_id 保持一致"
    )
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='generation_tasks',
        help_text="发起任务的用户"
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='PENDING',
        help_text="任务状态"
    )
    
    source_photo_url = models.TextField(
        help_text="用户上传的原始 2D 照片在对象存储中的 URL"
    )
    
    quality = models.CharField(
        max_length=10,
        choices=[
            ('high', 'High'),
            ('medium', 'Medium'),
            ('low', 'Low'),
            ('flux', 'Flux'),
            ('test', 'Test'),
        ],
        default='high',
        help_text="生成质量等级"
    )
    
    character = models.ForeignKey(
        'characters.Character',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='generation_task',
        help_text="任务成功后，生成的角色ID。失败则为 NULL"
    )
    
    error_message = models.TextField(
        null=True,
        blank=True,
        help_text="如果任务失败，记录错误原因"
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text="任务创建时间"
    )
    
    completed_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="任务完成（成功或失败）的时间"
    )
    
    class Meta:
        db_table = 'character_generation_tasks'
        indexes = [
            models.Index(fields=['user'], name='idx_task_user'),
            models.Index(fields=['status'], name='idx_task_status'),
            models.Index(fields=['created_at'], name='idx_task_created'),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Task {self.id} - {self.user.email} - {self.status}"
