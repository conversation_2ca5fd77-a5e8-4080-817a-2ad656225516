"""
Django 端的 Celery 任务定义
这些任务将在 worker 端执行
"""

from celery import shared_task
from django.utils import timezone
from .models import CharacterGenerationTask
from apps.characters.models import Character
import logging

logger = logging.getLogger(__name__)


@shared_task(bind=True, name='generate_3d_model')
def generate_3d_model(self, user_id, photo_object_name, quality="high"):
    """
    3D 模型生成任务
    
    这个任务定义在 Django 端，但实际执行在 worker 端
    Django 端只负责任务调度和状态查询
    """
    # 这个任务的实际执行逻辑在 worker 端
    # Django 端只是定义任务接口
    pass


@shared_task(bind=True, name='update_task_result', max_retries=3, default_retry_delay=60)
def update_task_result(self, task_id, status, result_data=None, error_message=None):
    """
    回调任务：处理GPU任务完成后的结果，更新数据库
    """
    logger.info(f"Processing callback for task {task_id}, status: {status}")
    try:
        # 查找对应的数据库任务记录
        task = CharacterGenerationTask.objects.get(id=task_id)
        logger.info(f"Found task record for {task_id}, current status: {task.status}")
        
        if status == 'SUCCESS':
            # 更新任务状态为成功
            task.status = 'SUCCESS'
            task.completed_at = timezone.now()
            
            # 如果任务返回了模型URL，创建角色记录
            if result_data and result_data.get('model_url'):
                model_url = result_data['model_url']
                thumbnail_url = result_data.get('thumbnail_url')  # 获取缩略图URL
                
                logger.info(f"Creating character with model_url: {model_url}, thumbnail_url: {thumbnail_url}")
                
                # 创建角色记录
                character = Character.objects.create(
                    owner=task.user,
                    name=f"Character {task.id}",  # 默认名称，用户可以修改
                    model_url=model_url,
                    thumbnail_url=thumbnail_url,  # 添加缩略图URL
                    metadata={
                        'generation_task_id': str(task.id),
                        'created_from_task': True
                    }
                )
                
                # 关联角色到任务
                task.character = character
                
                logger.info(f"Created character {character.id} for successful task {task_id}")
            
            task.save()
            logger.info(f"Updated task {task_id} status to SUCCESS")
            
        elif status == 'FAILURE':
            # 更新任务状态为失败
            task.status = 'FAILED'
            task.completed_at = timezone.now()
            task.error_message = error_message or 'Unknown error'
            
            task.save()
            logger.info(f"Updated task {task_id} status to FAILED: {task.error_message}")
            
    except CharacterGenerationTask.DoesNotExist:
        logger.warning(f"Task {task_id} not found in database")
        # 任务记录不存在，不需要重试
    except Exception as e:
        logger.error(f"Error updating task result for {task_id}: {e}")
        # 对于其他异常，尝试重试
        try:
            self.retry(countdown=60, exc=e)
        except self.MaxRetriesExceededError:
            logger.error(f"Max retries exceeded for task {task_id}, giving up") 