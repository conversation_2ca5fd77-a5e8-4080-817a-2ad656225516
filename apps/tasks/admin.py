from django.contrib import admin
from .models import CharacterGenerationTask


@admin.register(CharacterGenerationTask)
class CharacterGenerationTaskAdmin(admin.ModelAdmin):
    list_display = ['id', 'user', 'status', 'character', 'created_at', 'completed_at']
    list_filter = ['status', 'created_at', 'completed_at']
    search_fields = ['user__email', 'id']
    readonly_fields = ['id', 'created_at', 'completed_at']
    raw_id_fields = ['user', 'character']
    
    fieldsets = [
        (None, {
            'fields': ['id', 'user', 'status']
        }),
        ('任务详情', {
            'fields': ['source_photo_url', 'character', 'error_message']
        }),
        ('时间信息', {
            'fields': ['created_at', 'completed_at'],
            'classes': ['collapse']
        })
    ]
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'character')
