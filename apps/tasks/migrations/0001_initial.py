# Generated by Django 5.2.3 on 2025-06-27 15:19

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('characters', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CharacterGenerationTask',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='任务ID，与 Celery 的 task_id 保持一致', primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('PROCESSING', 'Processing'), ('SUCCESS', 'Success'), ('FAILED', 'Failed')], default='PENDING', help_text='任务状态', max_length=20)),
                ('source_photo_url', models.TextField(help_text='用户上传的原始 2D 照片在对象存储中的 URL')),
                ('error_message', models.TextField(blank=True, help_text='如果任务失败，记录错误原因', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='任务创建时间')),
                ('completed_at', models.DateTimeField(blank=True, help_text='任务完成（成功或失败）的时间', null=True)),
                ('character', models.ForeignKey(blank=True, help_text='任务成功后，生成的角色ID。失败则为 NULL', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='generation_task', to='characters.character')),
                ('user', models.ForeignKey(help_text='发起任务的用户', on_delete=django.db.models.deletion.CASCADE, related_name='generation_tasks', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'character_generation_tasks',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user'], name='idx_task_user'), models.Index(fields=['status'], name='idx_task_status'), models.Index(fields=['created_at'], name='idx_task_created')],
            },
        ),
    ]
