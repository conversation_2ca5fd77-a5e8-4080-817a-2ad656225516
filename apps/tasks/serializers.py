from rest_framework import serializers
from .models import CharacterGenerationTask


class CharacterGenerationTaskSerializer(serializers.ModelSerializer):
    user_email = serializers.CharField(source='user.email', read_only=True)
    character_name = serializers.CharField(source='character.name', read_only=True)
    
    class Meta:
        model = CharacterGenerationTask
        fields = [
            'id', 'user_email', 'status', 'source_photo_url', 'quality',
            'character', 'character_name', 'error_message', 
            'created_at', 'completed_at'
        ]
        read_only_fields = [
            'id', 'user_email', 'status', 'character', 'character_name',
            'error_message', 'created_at', 'completed_at'
        ]


class CreateGenerationTaskSerializer(serializers.Serializer):
    source_photo_key = serializers.CharField(max_length=500)
    quality = serializers.ChoiceField(
        choices=['high', 'medium', 'low', 'flux', 'test'], 
        default='high'
    )