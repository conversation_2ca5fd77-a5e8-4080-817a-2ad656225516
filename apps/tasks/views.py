from celery import current_app
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
import logging

logger = logging.getLogger(__name__)


def extract_task_info(task_result):
    """
    提取任务信息
    """
    try:
        if task_result.info is None:
            return {}
        
        if isinstance(task_result.info, dict):
            return task_result.info
        else:
            return {'message': str(task_result.info)}
    except Exception as e:
        logger.warning(f"Error extracting task info: {e}")
        return {'message': 'Unable to extract task information'}

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_task_status(request, task_id):
    """
    查询任务状态，同时同步更新数据库记录
    """
    try:
        # 从Celery获取任务状态
        task_result = current_app.AsyncResult(task_id)
        task_state = task_result.state
        
        # 根据Celery状态构建响应和同步数据0库
        if task_state == 'PENDING':
            response = {
                'task_id': task_id,
                'status': 'PENDING',
                'progress': 0,
                'message': 'Task is waiting to be processed'
            }
            # 数据库状态保持PENDING，无需更新
            
        elif task_state.startswith('PROCESS'):
            meta = extract_task_info(task_result)
            response = {
                'task_id': task_id,
                'status': 'PROCESSING',
                'progress': meta.get('progress', 0),
                'message': meta.get('message', 'Processing...'),
                'current_step': meta.get('status', 'unknown')
            }
                
        elif task_state == 'SUCCESS':
            meta = extract_task_info(task_result)
            
            response = {
                'task_id': task_id,
                'status': 'SUCCESS',
                'progress': 100,
                'message': meta.get('message', 'Task completed successfully'),
                'result': task_result.result
            }
            
        elif task_state == 'FAILURE':
            meta = extract_task_info(task_result)
            progress = meta.get('progress', 0) if isinstance(meta, dict) else 0
            error_message = meta.get('error', 'Unknown error')
            
            response = {
                'task_id': task_id,
                'status': 'FAILURE',
                'error_message': meta.get('message', 'Task failed'),
                'error': error_message,
                'progress': progress
            }
            
        else:
            info = task_result.info
            message = str(info) if info else 'Unknown status'
            
            response = {
                'task_id': task_id,
                'status': task_state,
                'message': message
            }
        
        return Response(response)
    
    except Exception as e:
        logger.error(f"Error getting task status for {task_id}: {e}")
        return Response(
            {'error': 'Failed to get task status'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
