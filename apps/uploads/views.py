from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from services.storage_service import storage_service
import logging
import requests
from django.core.files.uploadedfile import UploadedFile

logger = logging.getLogger(__name__)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def get_presigned_upload_url(request):
    """获取文件上传的预签名URL"""
    file_name = request.data.get('file_name')
    file_type = request.data.get('file_type')
    file_size = request.data.get('file_size')
    
    if not file_name or not file_type:
        return Response(
            {'error': 'file_name and file_type are required'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # 验证文件类型
    allowed_types = ['image/jpeg', 'image/png', 'image/jpg']
    if file_type not in allowed_types:
        return Response(
            {'error': 'Only JPEG and PNG images are allowed'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # 验证文件大小（最大10MB）
    max_size = 10 * 1024 * 1024  # 10MB
    try:
        file_size = int(file_size) if file_size else 0
    except (ValueError, TypeError):
        return Response({'error': 'Invalid file_size'}, status=status.HTTP_400_BAD_REQUEST)
    
    if file_size and file_size > max_size:
        return Response(
            {'error': 'File size exceeds 10MB limit'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    try:
        # 确保存储桶存在
        if not storage_service.ensure_bucket_exists():
            return Response(
                {'error': 'Storage service unavailable'}, 
                status=status.HTTP_503_SERVICE_UNAVAILABLE
            )
        
        # 生成预签名URL
        result = storage_service.generate_presigned_upload_url(
            file_name=file_name,
            file_type=file_type,
            expires_in=900  # 15分钟有效期
        )
        
        if not result:
            return Response(
                {'error': 'Failed to generate upload URL'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
        return Response({
            'upload_url': result['upload_url'],
            'file_key': result['file_key'],
            'expires_at': result['expires_at'].isoformat()
        })
    
    except Exception as e:
        logger.error(f"Error in get_presigned_upload_url: {e}")
        return Response(
            {'error': 'Internal server error'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def proxy_upload(request):
    """代理上传文件到MinIO存储"""
    try:
        # 获取上传的文件
        uploaded_file = request.FILES.get('file')
        upload_url = request.POST.get('upload_url')
        
        if not uploaded_file:
            return Response(
                {'error': 'No file provided'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if not upload_url:
            return Response(
                {'error': 'No upload URL provided'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 验证文件类型
        allowed_types = ['image/jpeg', 'image/png', 'image/jpg']
        if uploaded_file.content_type not in allowed_types:
            return Response(
                {'error': 'Only JPEG and PNG images are allowed'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 验证文件大小（最大10MB）
        max_size = 10 * 1024 * 1024  # 10MB
        if uploaded_file.size > max_size:
            return Response(
                {'error': 'File size exceeds 10MB limit'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 读取文件内容
        file_content = uploaded_file.read()
        
        # 向MinIO发送PUT请求
        headers = {
            'Content-Type': uploaded_file.content_type
        }
        
        response = requests.put(
            upload_url,
            data=file_content,
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            logger.info(f"File uploaded successfully: {uploaded_file.name}")
            return Response({
                'message': 'File uploaded successfully',
                'file_name': uploaded_file.name,
                'file_size': uploaded_file.size
            })
        else:
            logger.error(f"Upload failed with status {response.status_code}: {response.text}")
            return Response(
                {'error': f'Upload failed: {response.status_code}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    except requests.exceptions.RequestException as e:
        logger.error(f"Network error during upload: {e}")
        return Response(
            {'error': 'Network error during upload'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    except Exception as e:
        logger.error(f"Error in proxy_upload: {e}")
        return Response(
            {'error': 'Internal server error'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
