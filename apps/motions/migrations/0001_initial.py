# Generated by Django 5.2.3 on 2025-07-03 14:19

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Motion',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='动作唯一标识符', primary_key=True, serialize=False)),
                ('name', models.CharField(help_text='动作名称', max_length=100)),
                ('motion_url', models.TextField(help_text='动作文件在对象存储中的 URL')),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='存储其他元数据，如动作类型、标签等')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='创建时间')),
                ('creator', models.ForeignKey(help_text='该动作的创建者', on_delete=django.db.models.deletion.CASCADE, related_name='motions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'motions',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['creator'], name='idx_motion_creator'), models.Index(fields=['created_at'], name='idx_motion_created')],
            },
        ),
    ]
