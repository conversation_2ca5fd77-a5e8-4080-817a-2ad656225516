{% extends "admin/change_form.html" %}
{% load static %}

{% block extrahead %}
    {{ block.super }}
    <link rel="stylesheet" type="text/css" href="{% static 'admin/css/motion_admin.css' %}">
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 通用文件上传交互增强函数
            function setupFileUpload(selector, highlightColor, backgroundColor) {
                const fileInput = document.querySelector(selector);
                if (fileInput) {
                    // 文件选择后显示文件名
                    fileInput.addEventListener('change', function() {
                        const fileName = this.files[0] ? this.files[0].name : '未选择文件';
                        const fileSize = this.files[0] ? (this.files[0].size / 1024 / 1024).toFixed(2) + ' MB' : '';
                        
                        // 创建或更新文件信息显示
                        let fileInfo = this.parentNode.querySelector('.file-info');
                        if (!fileInfo) {
                            fileInfo = document.createElement('div');
                            fileInfo.className = 'file-info';
                            this.parentNode.appendChild(fileInfo);
                        }
                        
                        if (this.files[0]) {
                            fileInfo.innerHTML = `
                                <strong>已选择文件:</strong> ${fileName}<br>
                                <strong>文件大小:</strong> ${fileSize}
                            `;
                            fileInfo.style.cssText = `
                                margin-top: 10px;
                                padding: 8px 12px;
                                background-color: ${backgroundColor};
                                border-left: 4px solid ${highlightColor};
                                border-radius: 3px;
                                font-size: 13px;
                            `;
                        } else {
                            fileInfo.innerHTML = '';
                        }
                    });
                    
                    // 拖拽上传支持
                    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                        fileInput.addEventListener(eventName, preventDefaults, false);
                    });
                    
                    function preventDefaults(e) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                    
                    ['dragenter', 'dragover'].forEach(eventName => {
                        fileInput.addEventListener(eventName, highlight, false);
                    });
                    
                    ['dragleave', 'drop'].forEach(eventName => {
                        fileInput.addEventListener(eventName, unhighlight, false);
                    });
                    
                    function highlight(e) {
                        fileInput.style.borderColor = highlightColor;
                        fileInput.style.backgroundColor = backgroundColor;
                    }
                    
                    function unhighlight(e) {
                        fileInput.style.borderColor = '#ddd';
                        fileInput.style.backgroundColor = '#f9f9f9';
                    }
                    
                    fileInput.addEventListener('drop', handleDrop, false);
                    
                    function handleDrop(e) {
                        const dt = e.dataTransfer;
                        const files = dt.files;
                        
                        if (files.length > 0) {
                            fileInput.files = files;
                            // 触发change事件
                            const event = new Event('change', { bubbles: true });
                            fileInput.dispatchEvent(event);
                        }
                    }
                }
            }
            
            // 设置动作文件上传
            setupFileUpload('.motion-file-upload', '#007cba', '#f0f8ff');
            
            // 设置预览视频上传
            setupFileUpload('.preview-video-upload', '#28a745', '#f0fff0');
        });
    </script>
{% endblock %}

{% block form_top %}
    {{ block.super }}
    {% if form.motion_file.help_text %}
        <div class="motion-upload-instructions" style="
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-size: 14px;
        ">
            <h3 style="margin-top: 0; color: #495057;">📁 动作文件上传说明</h3>
            <ul style="margin-bottom: 0; color: #6c757d;">
                <li><strong>动作文件:</strong> FBX, BVH, GLB, GLTF, Blend, DAE, OBJ（最大50MB）</li>
                <li><strong>预览视频:</strong> MP4, MOV, AVI, MKV, WebM（最大100MB）</li>
                <li><strong>拖拽上传:</strong> 支持拖拽文件到对应的上传区域</li>
                <li><strong>自动处理:</strong> 上传后系统将自动保存到对象存储</li>
                <li><strong>可选上传:</strong> 预览视频为可选项，可单独上传动作文件</li>
            </ul>
        </div>
    {% endif %}
{% endblock %}