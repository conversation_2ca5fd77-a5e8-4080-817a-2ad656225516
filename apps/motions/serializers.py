from rest_framework import serializers
from .models import Motion


class MotionListSerializer(serializers.ModelSerializer):
    """动作列表序列化器，包含创建者信息"""
    
    creator = serializers.SerializerMethodField()
    
    class Meta:
        model = Motion
        fields = [
            'id', 'name', 'motion_url', 'preview_video_url', 'creator',
            'metadata', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']
    
    def get_creator(self, obj):
        """返回创建者ID"""
        return str(obj.creator.id)


class CreateMotionSerializer(serializers.ModelSerializer):
    """创建动作序列化器"""
    
    class Meta:
        model = Motion
        fields = ['name', 'motion_url', 'preview_video_url', 'metadata']
        
    def create(self, validated_data):
        validated_data['creator'] = self.context['request'].user
        return super().create(validated_data)