import os
import uuid
from django import forms
from django.contrib import admin
from django.utils.html import format_html
from django.contrib import messages
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from .models import Motion
from services.storage_service import storage_service
from django.contrib.auth import get_user_model

User = get_user_model()


class MotionAdminForm(forms.ModelForm):
    """自定义Motion管理表单"""
    motion_file = forms.FileField(
        required=False,
        help_text='上传动作文件（FBX、BVH、GLB等格式，最大50MB）',
        widget=forms.ClearableFileInput(attrs={
            'accept': '.fbx,.bvh,.glb,.gltf,.blend,.dae,.obj',
            'class': 'motion-file-upload'
        })
    )
    
    preview_video_file = forms.FileField(
        required=False,
        help_text='上传预览视频（MP4、MOV、AVI等格式，最大100MB）',
        widget=forms.ClearableFileInput(attrs={
            'accept': '.mp4,.mov,.avi,.mkv,.webm',
            'class': 'preview-video-upload'
        })
    )
    
    class Meta:
        model = Motion
        fields = ['name', 'creator', 'motion_url', 'preview_video_url', 'metadata']
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 如果是编辑现有动作，显示当前文件信息
        if self.instance and self.instance.pk:
            if self.instance.motion_url:
                self.fields['motion_file'].help_text = f'当前文件: {self.instance.motion_url}<br>上传新文件将替换现有文件'
            if self.instance.preview_video_url:
                self.fields['preview_video_file'].help_text = f'当前预览视频: {self.instance.preview_video_url}<br>上传新视频将替换现有视频'
        
        # motion_url和preview_video_url字段设为可选且只读（将通过文件上传自动生成）
        if 'motion_url' in self.fields:
            self.fields['motion_url'].required = False
            self.fields['motion_url'].widget.attrs['readonly'] = True
            self.fields['motion_url'].help_text = '系统自动生成，请通过上传文件更新'
        if 'preview_video_url' in self.fields:
            self.fields['preview_video_url'].required = False
            self.fields['preview_video_url'].widget.attrs['readonly'] = True
            self.fields['preview_video_url'].help_text = '系统自动生成，请通过上传视频更新'
    
    def clean_motion_file(self):
        """验证上传的动作文件"""
        motion_file = self.cleaned_data.get('motion_file')
        if motion_file:
            # 检查文件大小（最大50MB）
            if motion_file and motion_file.size and motion_file.size > 50 * 1024 * 1024:
                raise forms.ValidationError('文件大小不能超过50MB')
            
            # 检查文件扩展名
            allowed_extensions = ['.fbx', '.bvh', '.glb', '.gltf', '.blend', '.dae', '.obj']
            file_extension = os.path.splitext(motion_file.name)[1].lower()
            if file_extension not in allowed_extensions:
                raise forms.ValidationError(
                    f'不支持的文件格式。支持的格式: {", ".join(allowed_extensions)}'
                )
            
            # 基本的文件头验证（防止恶意文件）
            try:
                file_header = motion_file.read(1024)
                motion_file.seek(0)  # 重置文件指针
                
                # 检查是否为二进制文件（简单验证）
                if len(file_header) == 0:
                    raise forms.ValidationError('文件为空或无法读取')
                    
            except Exception as e:
                raise forms.ValidationError(f'文件验证失败: {str(e)}')
        
        return motion_file
    
    def clean_preview_video_file(self):
        """验证上传的预览视频文件"""
        preview_video_file = self.cleaned_data.get('preview_video_file')
        if preview_video_file:
            # 检查文件大小（最大100MB）
            if preview_video_file and preview_video_file.size and preview_video_file.size > 100 * 1024 * 1024:
                raise forms.ValidationError('预览视频文件大小不能超过100MB')
            
            # 检查文件扩展名
            allowed_extensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm']
            file_extension = os.path.splitext(preview_video_file.name)[1].lower()
            if file_extension not in allowed_extensions:
                raise forms.ValidationError(
                    f'不支持的视频格式。支持的格式: {", ".join(allowed_extensions)}'
                )
            
            # 基本的文件头验证
            try:
                file_header = preview_video_file.read(1024)
                preview_video_file.seek(0)  # 重置文件指针
                
                if len(file_header) == 0:
                    raise forms.ValidationError('视频文件为空或无法读取')
                    
            except Exception as e:
                raise forms.ValidationError(f'视频文件验证失败: {str(e)}')
        
        return preview_video_file
    
    def clean_creator(self):
        """验证创建者权限"""
        creator = self.cleaned_data.get('creator')
        if creator and creator.provider != 'admin':
            raise forms.ValidationError('只有管理员用户可以作为动作创建者')
        return creator
    
    def clean(self):
        """表单整体验证"""
        cleaned_data = super().clean()
        motion_file = cleaned_data.get('motion_file')
        
        # 如果是新建动作，必须上传文件
        if not self.instance.pk:  # 新建
            if not motion_file:
                raise forms.ValidationError('请上传动作文件')
            # 新建时清空URL字段，由文件上传生成
            cleaned_data['motion_url'] = ''
            cleaned_data['preview_video_url'] = ''
        
        # 如果是编辑现有动作，且没有上传新文件且没有现有URL，则需要上传文件
        elif not motion_file and not self.instance.motion_url:
            raise forms.ValidationError('请上传动作文件')
        
        return cleaned_data
    
    def save(self, commit=True):
        """保存时处理文件上传"""
        instance = super().save(commit=False)
        motion_file = self.cleaned_data.get('motion_file')
        preview_video_file = self.cleaned_data.get('preview_video_file')
        
        # 如果没有上传新文件但是在编辑现有动作，保持原有URL
        if not motion_file and self.instance.pk and self.instance.motion_url:
            instance.motion_url = self.instance.motion_url
        
        # 如果没有上传新预览视频但是在编辑现有动作，保持原有预览视频URL
        if not preview_video_file and self.instance.pk and self.instance.preview_video_url:
            instance.preview_video_url = self.instance.preview_video_url
        
        # 如果上传了文件，处理文件上传
        if motion_file:
            # 生成唯一的文件键
            file_extension = os.path.splitext(motion_file.name)[1]
            file_key = f"motions/{uuid.uuid4()}{file_extension}"
            temp_file_path = None
            
            try:
                # 确保存储服务可用
                if not storage_service.ensure_bucket_exists():
                    raise forms.ValidationError('存储服务不可用，请联系管理员')
                
                # 将文件内容写入临时文件
                temp_file_path = f"/tmp/{uuid.uuid4()}{file_extension}"
                with open(temp_file_path, 'wb+') as temp_file:
                    for chunk in motion_file.chunks():
                        temp_file.write(chunk)
                
                # 上传到对象存储
                if storage_service.upload_file(temp_file_path, file_key):
                    # 生成文件URL
                    instance.motion_url = storage_service.get_file_url(file_key)
                    
                    # 验证文件是否真的上传成功
                    if not storage_service.file_exists(file_key):
                        raise forms.ValidationError('文件上传验证失败，请重试')
                else:
                    raise forms.ValidationError('文件上传失败，请检查网络连接并重试')
                    
            except forms.ValidationError:
                # 重新抛出表单验证错误
                raise
            except Exception as e:
                # 捕获其他所有异常
                raise forms.ValidationError(f'文件上传过程中发生错误: {str(e)}')
            finally:
                # 确保清理临时文件
                if temp_file_path and os.path.exists(temp_file_path):
                    try:
                        os.remove(temp_file_path)
                    except OSError:
                        pass  # 忽略临时文件删除失败
        
        # 如果上传了预览视频，处理预览视频上传
        if preview_video_file:
            # 生成唯一的视频文件键
            video_extension = os.path.splitext(preview_video_file.name)[1]
            video_file_key = f"motions/previews/{uuid.uuid4()}{video_extension}"
            temp_video_path = None
            
            try:
                # 确保存储服务可用
                if not storage_service.ensure_bucket_exists():
                    raise forms.ValidationError('存储服务不可用，请联系管理员')
                
                # 将视频文件内容写入临时文件
                temp_video_path = f"/tmp/{uuid.uuid4()}{video_extension}"
                with open(temp_video_path, 'wb+') as temp_file:
                    for chunk in preview_video_file.chunks():
                        temp_file.write(chunk)
                
                # 上传到对象存储
                if storage_service.upload_file(temp_video_path, video_file_key):
                    # 生成预览视频URL
                    instance.preview_video_url = storage_service.get_file_url(video_file_key)
                    
                    # 验证文件是否真的上传成功
                    if not storage_service.file_exists(video_file_key):
                        raise forms.ValidationError('预览视频上传验证失败，请重试')
                else:
                    raise forms.ValidationError('预览视频上传失败，请检查网络连接并重试')
                    
            except forms.ValidationError:
                # 重新抛出表单验证错误
                raise
            except Exception as e:
                # 捕获其他所有异常
                raise forms.ValidationError(f'预览视频上传过程中发生错误: {str(e)}')
            finally:
                # 确保清理临时文件
                if temp_video_path and os.path.exists(temp_video_path):
                    try:
                        os.remove(temp_video_path)
                    except OSError:
                        pass  # 忽略临时文件删除失败
        
        if commit:
            instance.save()
        return instance


@admin.register(Motion)
class MotionAdmin(admin.ModelAdmin):
    form = MotionAdminForm
    list_display = ['name', 'creator_link', 'motion_url_link', 'preview_video_link', 'created_at']
    list_filter = ['created_at', 'creator']
    search_fields = ['name', 'creator__email', 'creator__nickname']
    readonly_fields = ['id', 'created_at']
    raw_id_fields = ['creator']
    
    fieldsets = [
        (None, {
            'fields': ['id', 'name', 'creator']
        }),
        ('文件上传', {
            'fields': ['motion_file', 'preview_video_file'],
            'description': '上传动作文件和预览视频，系统将自动保存到对象存储并生成URL'
        }),
        ('生成的URLs', {
            'fields': ['motion_url', 'preview_video_url'],
            'classes': ['collapse'],
            'description': '系统自动生成的文件URL，只读字段'
        }),
        ('元数据', {
            'fields': ['metadata'],
            'classes': ['collapse']
        }),
        ('时间信息', {
            'fields': ['created_at'],
            'classes': ['collapse']
        })
    ]
    
    def creator_link(self, obj):
        """显示创建者链接"""
        return format_html(
            '<a href="/admin/users/user/{}/change/">{}</a>',
            obj.creator.id,
            obj.creator.email
        )
    creator_link.short_description = '创建者'
    creator_link.admin_order_field = 'creator__email'
    
    def motion_url_link(self, obj):
        """显示动作文件链接"""
        if obj.motion_url:
            return format_html(
                '<a href="{}" target="_blank">查看文件</a>',
                obj.motion_url
            )
        return '-'
    motion_url_link.short_description = '动作文件'
    
    def preview_video_link(self, obj):
        """显示预览视频链接"""
        if obj.preview_video_url:
            return format_html(
                '<a href="{}" target="_blank">预览视频</a>',
                obj.preview_video_url
            )
        return '-'
    preview_video_link.short_description = '预览视频'
    
    # 批量操作
    actions = ['make_backup', 'export_selected']
    
    def make_backup(self, request, queryset):
        """批量备份动作"""
        count = queryset.count()
        self.message_user(request, f'已标记 {count} 个动作进行备份')
    make_backup.short_description = '备份选中的动作'
    
    def export_selected(self, request, queryset):
        """导出选中的动作信息"""
        count = queryset.count()
        self.message_user(request, f'已导出 {count} 个动作的信息')
    export_selected.short_description = '导出选中的动作信息'
    
    def save_model(self, request, obj, form, change):
        """保存模型时的额外处理"""
        # 如果没有设置creator，默认为当前用户
        if not obj.creator_id:
            obj.creator = request.user
        
        super().save_model(request, obj, form, change)
        
        # 显示成功消息
        success_messages = []
        if form.cleaned_data.get('motion_file'):
            success_messages.append('动作文件已成功上传')
        if form.cleaned_data.get('preview_video_file'):
            success_messages.append('预览视频已成功上传')
        
        if success_messages:
            messages.success(request, f'{obj.name}: {", ".join(success_messages)}')
    
    def get_form(self, request, obj=None, **kwargs):
        """自定义表单"""
        form = super().get_form(request, obj, **kwargs)
        
        # 为新建动作时，creator字段默认为当前用户
        if not obj and 'creator' in form.base_fields:
            form.base_fields['creator'].initial = request.user
            
        return form
    
    def has_module_permission(self, request):
        """检查用户是否有访问motion模块的权限"""
        return request.user.is_authenticated and request.user.provider == 'admin'
    
    def has_view_permission(self, request, obj=None):
        """检查查看权限"""
        return request.user.is_authenticated and request.user.provider == 'admin'
    
    def has_add_permission(self, request):
        """检查添加权限"""
        return request.user.is_authenticated and request.user.provider == 'admin'
    
    def has_change_permission(self, request, obj=None):
        """检查修改权限"""
        return request.user.is_authenticated and request.user.provider == 'admin'
    
    def has_delete_permission(self, request, obj=None):
        """检查删除权限"""
        return request.user.is_authenticated and request.user.provider == 'admin'
    
    def get_queryset(self, request):
        """根据用户权限过滤查询集"""
        qs = super().get_queryset(request)
        if not request.user.is_authenticated or request.user.provider != 'admin':
            # 非管理员用户看不到任何动作
            return qs.none()
        return qs
    
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """自定义外键字段选项"""
        if db_field.name == "creator":
            # 只显示管理员用户作为创建者选项
            kwargs["queryset"] = User.objects.filter(provider='admin')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
