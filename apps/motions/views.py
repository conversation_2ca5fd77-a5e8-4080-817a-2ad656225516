from django.core.paginator import Paginator
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .models import Motion
from .serializers import MotionListSerializer
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_all_motions(request):
    """
    获取所有动作列表
    
    返回系统中所有用户创建的动作，支持分页查询
    """
    try:
        # 获取查询参数
        try:
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 12))  # 移动端优化：默认12条
        except (ValueError, TypeError):
            page = 1
            page_size = 12
        
        # 限制页面大小
        if page_size > 50:  # 移动端优化：最大50条
            page_size = 50
        if page_size < 1:
            page_size = 12
        if page < 1:
            page = 1
        
        # 查询所有动作，按创建时间倒序
        motions_queryset = Motion.objects.all().order_by('-created_at')
        
        # 分页
        paginator = Paginator(motions_queryset, page_size)
        
        # 检查页码是否有效
        if page > paginator.num_pages and paginator.num_pages > 0:
            page = paginator.num_pages
        
        motions_page = paginator.get_page(page)
        
        # 序列化数据
        serializer = MotionListSerializer(motions_page.object_list, many=True)
        
        # 构建响应
        response_data = {
            'total': paginator.count,
            'page': page,
            'page_size': page_size,
            'total_pages': paginator.num_pages,
            'motions': serializer.data
        }
        
        logger.info(f"Retrieved {len(serializer.data)} motions, page {page}")
        
        return Response(response_data)
    
    except ValueError as e:
        logger.warning(f"Invalid pagination parameters: {e}")
        return Response(
            {'error': 'Invalid pagination parameters'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        logger.error(f"Error retrieving motions: {e}")
        return Response(
            {'error': 'Failed to retrieve motions'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
