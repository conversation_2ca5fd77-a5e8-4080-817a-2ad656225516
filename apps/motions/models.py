import uuid
from django.db import models
from django.contrib.auth import get_user_model


User = get_user_model()


class Motion(models.Model):
    """
    动作库表 - 存储所有用户创建的动作文件
    """
    
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text="动作唯一标识符"
    )
    
    creator = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='motions',
        help_text="该动作的创建者"
    )
    
    name = models.CharField(
        max_length=100,
        help_text="动作名称"
    )
    
    motion_url = models.TextField(
        help_text="动作文件在对象存储中的 URL"
    )
    
    preview_video_url = models.TextField(
        blank=True,
        null=True,
        help_text="预览视频文件在对象存储中的 URL"
    )
    
    metadata = models.JSONField(
        default=dict,
        blank=True,
        help_text="存储其他元数据，如动作类型、标签等"
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text="创建时间"
    )
    
    class Meta:
        db_table = 'motions'
        indexes = [
            models.Index(fields=['creator'], name='idx_motion_creator'),
            models.Index(fields=['created_at'], name='idx_motion_created'),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} - {self.creator.email}"
