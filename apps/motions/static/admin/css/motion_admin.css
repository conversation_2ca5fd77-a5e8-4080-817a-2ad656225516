/* Motion Admin Custom Styles */

.motion-file-upload, .preview-video-upload {
    padding: 10px;
    border: 2px dashed #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
    transition: all 0.3s ease;
}

.motion-file-upload:hover, .preview-video-upload:hover {
    border-color: #007cba;
    background-color: #f0f8ff;
}

.motion-file-upload:focus, .preview-video-upload:focus {
    border-color: #005a87;
    box-shadow: 0 0 5px rgba(0, 124, 186, 0.3);
    outline: none;
}

/* 预览视频特殊样式 */
.preview-video-upload {
    border-color: #28a745;
}

.preview-video-upload:hover {
    border-color: #218838;
    background-color: #f0fff0;
}

/* 文件上传提示样式 */
.field-motion_file .help, .field-preview_video_file .help {
    margin-top: 8px;
    padding: 8px 12px;
    background-color: #e7f3ff;
    border-left: 4px solid #007cba;
    border-radius: 3px;
    font-size: 13px;
    line-height: 1.4;
}

.field-preview_video_file .help {
    background-color: #e8f5e8;
    border-left-color: #28a745;
}

/* 支持的文件格式显示 */
.field-motion_file .help::before {
    content: "📁 ";
    font-size: 14px;
}

.field-preview_video_file .help::before {
    content: "🎬 ";
    font-size: 14px;
}

/* 列表页面优化 */
.column-motion_url_link, .column-preview_video_link {
    max-width: 150px;
    word-break: break-all;
}

.column-creator_link {
    min-width: 120px;
}

/* 表单字段集样式优化 */
.aligned .form-row .field-motion_file, .aligned .form-row .field-preview_video_file {
    margin-bottom: 20px;
}

/* 错误消息样式 */
.field-motion_file .errorlist, .field-preview_video_file .errorlist {
    margin-top: 5px;
    padding: 8px 12px;
    background-color: #ffeaea;
    border-left: 4px solid #dc3545;
    border-radius: 3px;
    color: #721c24;
}

/* 成功消息样式 */
.messages .success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
    padding: 12px 20px;
    border-radius: 5px;
    margin-bottom: 20px;
}

/* 批量操作样式 */
.actions select {
    min-width: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .column-motion_url_link, .column-preview_video_link {
        max-width: 100px;
    }
    
    .field-motion_file .help, .field-preview_video_file .help {
        font-size: 12px;
        padding: 6px 10px;
    }
}