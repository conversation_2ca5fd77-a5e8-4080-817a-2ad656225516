from django.contrib.auth import get_user_model
from django.core.paginator import Paginator
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from mokta.celery import app as celery_app
from services.storage_service import storage_service
from apps.tasks.models import CharacterGenerationTask
from apps.tasks.serializers import CharacterGenerationTaskSerializer
from .models import Character
from .serializers import CharacterListSerializer
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_character_generation_task(request):
    """
    创建3D模型生成任务
    
    Django 服务器创建 Celery 任务，任务将在另一台服务器的 Celery worker 上执行
    """
    source_photo_key = request.data.get('source_photo_key')
    quality = request.data.get('quality', 'high')  # 生成质量
    
    if not source_photo_key:
        return Response(
            {'error': 'source_photo_key is required'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # 检查每日限额
    if not request.user.can_generate_today():
        today_count = request.user.get_today_generation_count()
        return Response(
            {
                'error': 'daily_limit_exceeded',
                'message': f'今日生成次数已达上限（{today_count}/{request.user.daily_generation_limit}）',
                'today_usage': {
                    'total_generations': today_count,
                    'daily_limit': request.user.daily_generation_limit,
                    'remaining': request.user.get_remaining_generations_today()
                }
            }, 
            status=status.HTTP_429_TOO_MANY_REQUESTS
        )
    
    # 验证质量参数
    if quality not in ['high', 'medium', 'low', 'flux', 'test']:
        quality = 'high'
    
    # 验证文件是否存在
    if not storage_service.file_exists(source_photo_key):
        return Response(
            {'error': 'Source photo not found in storage'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    
    try:
        # 获取完整的存储URL
        source_photo_url = storage_service.get_file_url(source_photo_key)
        
        # 先创建数据库记录，确保数据一致性
        db_task = CharacterGenerationTask.objects.create(
            user=request.user,
            status='PENDING',
            source_photo_url=source_photo_url,
            quality=quality
        )
        
        try:
            # 创建 Celery 任务，使用数据库记录的ID作为task_id
            task = celery_app.send_task(
                'generate_3d_model',
                args=[str(request.user.id), source_photo_key, quality],
                queue='character_generation',
                task_id=str(db_task.id)  # 使用数据库记录的UUID
            )
            
            logger.info(f"Created 3D model generation task {db_task.id} for user {request.user.id}")
            
        except Exception as celery_error:
            # 如果Celery任务创建失败，删除数据库记录
            logger.error(f"Failed to create Celery task: {celery_error}")
            db_task.delete()
            raise celery_error
        
        # 返回序列化的任务数据
        serializer = CharacterGenerationTaskSerializer(db_task)
        
        return Response({
            'task_id': str(db_task.id),
            'status': 'PENDING',
            'message': '3D model generation task created',
            'quality': quality,
            'task': serializer.data
        })
    
    except Exception as e:
        logger.error(f"Error creating 3D model generation task: {e}")
        return Response(
            {'error': 'Failed to create generation task'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_characters(request):
    try:
        # 获取查询参数
        try:
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 12))  # 移动端优化：默认12条
        except (ValueError, TypeError):
            page = 1
            page_size = 12
        
        # 限制页面大小
        if page_size > 50:  # 移动端优化：最大50条
            page_size = 50
        if page_size < 1:
            page_size = 12
        if page < 1:
            page = 1
        
        # 查询用户的角色，按创建时间倒序
        characters_queryset = Character.objects.filter(
            owner=request.user
        ).order_by('-created_at')
        
        # 分页
        paginator = Paginator(characters_queryset, page_size)
        
        # 检查页码是否有效
        if page > paginator.num_pages and paginator.num_pages > 0:
            page = paginator.num_pages
        
        characters_page = paginator.get_page(page)
        
        # 序列化数据
        serializer = CharacterListSerializer(characters_page.object_list, many=True)
        
        # 构建响应
        response_data = {
            'total': paginator.count,
            'page': page,
            'page_size': page_size,
            'total_pages': paginator.num_pages,
            'characters': serializer.data
        }
        
        logger.info(f"Retrieved {len(serializer.data)} characters for user {request.user.id}, page {page}")
        
        return Response(response_data)
    
    except ValueError as e:
        logger.warning(f"Invalid pagination parameters: {e}")
        return Response(
            {'error': 'Invalid pagination parameters'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        logger.error(f"Error retrieving characters for user {request.user.id}: {e}")
        return Response(
            {'error': 'Failed to retrieve characters'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
