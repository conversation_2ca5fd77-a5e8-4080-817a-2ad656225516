from rest_framework import serializers
from .models import Character


class CharacterSerializer(serializers.ModelSerializer):
    owner_email = serializers.CharField(source='owner.email', read_only=True)
    
    class Meta:
        model = Character
        fields = [
            'id', 'owner_email', 'name', 'model_url', 'thumbnail_url',
            'metadata', 'created_at'
        ]
        read_only_fields = ['id', 'owner_email', 'created_at']


class CreateCharacterSerializer(serializers.ModelSerializer):
    class Meta:
        model = Character
        fields = ['name', 'model_url', 'thumbnail_url', 'metadata']
        
    def create(self, validated_data):
        validated_data['owner'] = self.context['request'].user
        return super().create(validated_data)


class CharacterListSerializer(serializers.ModelSerializer):
    """专门用于角色列表的序列化器，不包含owner_email字段"""
    
    class Meta:
        model = Character
        fields = [
            'id', 'name', 'model_url', 'thumbnail_url',
            'metadata', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']