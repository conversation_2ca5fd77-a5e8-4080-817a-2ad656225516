import uuid
from django.db import models
from django.contrib.auth import get_user_model


User = get_user_model()


class Character(models.Model):
    """
    角色库表 - 存储所有成功生成的3D虚拟角色
    """
    
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text="角色唯一标识符"
    )
    
    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='characters',
        help_text="该角色的拥有者"
    )
    
    name = models.CharField(
        max_length=100,
        help_text="角色名称 (可由用户修改)"
    )
    
    model_url = models.TextField(
        help_text="角色 3D 模型文件在对象存储中的 URL"
    )
    
    thumbnail_url = models.TextField(
        blank=True,
        null=True,
        help_text="角色缩略图文件在对象存储中的 URL"
    )
    
    metadata = models.JSONField(
        default=dict,
        blank=True,
        help_text="存储其他元数据，如动画列表、特效配置等"
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text="创建时间"
    )
    
    class Meta:
        db_table = 'characters'
        indexes = [
            models.Index(fields=['owner'], name='idx_character_owner'),
            models.Index(fields=['created_at'], name='idx_character_created'),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} - {self.owner.email}"
