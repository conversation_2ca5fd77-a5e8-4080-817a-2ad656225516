from django.contrib import admin
from .models import Character


@admin.register(Character)
class CharacterAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'owner', 'thumbnail_url', 'created_at']
    list_filter = ['created_at', 'owner']
    search_fields = ['name', 'owner__email']
    readonly_fields = ['id', 'created_at']
    raw_id_fields = ['owner']
    
    fieldsets = [
        (None, {
            'fields': ['id', 'name', 'owner', 'model_url', 'thumbnail_url']
        }),
        ('元数据', {
            'fields': ['metadata'],
            'classes': ['collapse']
        }),
        ('时间信息', {
            'fields': ['created_at'],
            'classes': ['collapse']
        })
    ]
