# Generated by Django 5.2.3 on 2025-06-27 15:19

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Character',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='角色唯一标识符', primary_key=True, serialize=False)),
                ('name', models.CharField(help_text='角色名称 (可由用户修改)', max_length=100)),
                ('model_url', models.TextField(help_text='角色 3D 模型文件在对象存储中的 URL')),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='存储其他元数据，如动画列表、特效配置等')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='创建时间')),
                ('owner', models.ForeignKey(help_text='该角色的拥有者', on_delete=django.db.models.deletion.CASCADE, related_name='characters', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'characters',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['owner'], name='idx_character_owner'), models.Index(fields=['created_at'], name='idx_character_created')],
            },
        ),
    ]
