# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Environment variables
.env
.env.dev
.env.production

# Virtual environment
.venv/
venv/
ENV/
env/
bin/
include/
pyvenv.cfg

# IDE
.vscode/
.idea/
.claude/
*.swp
*.swo
*~

# MacOS
.DS_Store

# Logs
logs/
*.log

# Temporary files
*.tmp
*.temp

# SSL certificates
*.pem
*.key
*.crt

# Apple OAuth private keys
apple_private_key.p8