"""
Celery 配置

Django 服务器作为 Celery 客户端，创建任务并查询状态
另一台服务器运行 Celery Worker，执行实际的任务
"""

import os
from pathlib import Path
from celery import Celery
from django.conf import settings

# 加载环境变量
def load_env():
    """Load environment variables from .env file"""
    env_file = Path(__file__).parent.parent / '.env'
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ.setdefault(key, value)
    else:
        # 如果.env文件不存在，尝试加载默认环境变量
        # 这样在没有.env文件的情况下仍然可以使用系统环境变量
        pass

# 在导入settings之前加载环境变量
load_env()

# 设置 Django 环境变量
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mokta.settings')

# 确保Django被正确初始化
import django
django.setup()

# 创建 Celery 应用实例
# 必须与 celery_worker.py 中的应用名称一致
app = Celery('character_generator')

# 从 Django 设置中加载配置
app.config_from_object('django.conf:settings', namespace='CELERY')

# 自动发现任务
app.autodiscover_tasks()

# 任务路由配置
# 任务在 celery_worker.py 中定义，使用 character_generator 应用
app.conf.task_routes = {
    'generate_3d_model': {
        'queue': 'character_generation'
    },
    'update_task_result': {
        'queue': 'callback'
    },
}

# 任务配置
app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone=settings.TIME_ZONE,
    enable_utc=True,
    # 任务结果过期时间
    result_expires=3600,
    # 任务超时时间
    task_time_limit=1800,  # 30分钟
    task_soft_time_limit=1500,  # 25分钟软限制
    # 异常处理配置
    task_ignore_result=False,
    task_store_eager_result=True,
    task_store_failures_even_if_ignored=True,
    task_remote_tracebacks=True,
)


@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')


if __name__ == '__main__':
    app.start()