<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传图片并生成角色 - Mokta</title>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <h1>📸 上传图片并生成角色</h1>
    
    <!-- 用户登录状态 -->
    <div class="container">
        <h2>👤 登录状态</h2>
        <div id="loginStatus" class="status info">
            未登录 - 请先进行 Google 登录
        </div>
        
        <!-- Google 登录按钮 -->
        <div id="googleLogin">
            <div id="g_id_onload"
                 data-client_id="************-bpeara4jocr23ddgeg7t8g1u52vmthh8.apps.googleusercontent.com"
                 data-callback="handleCredentialResponse"
                 data-auto_prompt="false">
            </div>
            <div class="g_id_signin" 
                 data-type="standard" 
                 data-size="large" 
                 data-theme="outline" 
                 data-text="sign_in_with"
                 data-shape="rectangular"
                 data-logo_alignment="left">
            </div>
        </div>
        
        <!-- 用户信息显示 -->
        <div id="userInfo" class="user-info">
            <h3>✅ 登录成功</h3>
            <p><strong>邮箱:</strong> <span id="userEmail"></span></p>
            <p><strong>昵称:</strong> <span id="userNickname"></span></p>
            <button onclick="logout()">退出登录</button>
        </div>
    </div>

    <!-- 每日配额显示 -->
    <div class="container" id="quotaContainer" style="display: none;">
        <h2>📊 今日使用情况</h2>
        <div id="quotaInfo" class="quota-display">
            <div class="quota-progress">
                <div class="quota-bar">
                    <div id="quotaFill" class="quota-fill"></div>
                </div>
                <div class="quota-text">
                    <span id="quotaUsed">-</span> / <span id="quotaLimit">-</span> 次
                    (剩余 <span id="quotaRemaining">-</span> 次)
                </div>
            </div>
            <div id="quotaStatus" class="quota-status"></div>
        </div>
    </div>

    <!-- 主要功能区域 -->
    <div id="mainArea" class="hidden">
        
        <!-- 使用说明 -->
        <div class="container">
            <h2>📋 使用流程</h2>
            <div class="step">
                <span class="step-number">1</span>
                <strong>选择图片</strong> - 点击下方区域或拖拽图片文件
            </div>
            <div class="step">
                <span class="step-number">2</span>
                <strong>选择质量</strong> - 根据需要选择生成质量
            </div>
            <div class="step">
                <span class="step-number">3</span>
                <strong>开始生成</strong> - 点击"上传并生成角色"按钮
            </div>
            <div class="step">
                <span class="step-number">4</span>
                <strong>等待完成</strong> - 自动监控生成进度
            </div>
        </div>

        <!-- 图片上传区域 -->
        <div class="container">
            <h2>📤 选择图片</h2>
            
            <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                <div id="uploadPrompt">
                    <p style="font-size: 24px; margin: 0;">📁</p>
                    <p style="margin: 10px 0;">点击选择图片文件</p>
                    <p style="color: #666; margin: 0;">或将图片拖拽到此区域</p>
                    <p style="color: #999; font-size: 14px; margin-top: 10px;">支持 JPG、PNG 格式，最大 10MB</p>
                </div>
                <img id="previewImage" class="preview-image hidden" alt="预览图片">
            </div>
            
            <input type="file" id="fileInput" class="file-input" accept="image/jpeg,image/png" onchange="handleFileSelect(event)">
            
            <div id="fileInfo" class="status info hidden">
                <p><strong>文件名:</strong> <span id="fileName"></span></p>
                <p><strong>文件大小:</strong> <span id="fileSize"></span></p>
                <p><strong>文件类型:</strong> <span id="fileType"></span></p>
            </div>
        </div>

        <!-- 生成设置 -->
        <div class="container">
            <h2>⚙️ 生成设置</h2>
            
            <div class="quality-selector">
                <label for="qualitySelect">选择生成质量:</label>
                <select id="qualitySelect">
                    <option value="high">高质量 (推荐)</option>
                    <option value="medium">中等质量</option>
                    <option value="low">低质量 (快速)</option>
                    <option value="flux">超快速</option>
                    <option value="test">测试模式</option>
                </select>
            </div>
            
            <button id="generateBtn" onclick="startUploadAndGenerate()" disabled>
                🚀 上传并生成角色
            </button>
            
            <div class="progress-bar" id="progressBar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            
            <div id="statusMessage" class="status info hidden">
                准备开始...
            </div>
        </div>

        <!-- 我的角色库 -->
        <div class="container" id="charactersSection">
            <h2>🎭 我的角色库</h2>
            
            <div id="charactersStatus" class="status info">
                正在加载角色列表...
            </div>
            
            <div id="charactersGrid" class="characters-grid">
                <!-- 角色缩略图将动态插入这里 -->
            </div>
            
            <div id="charactersEmpty" class="empty-state hidden">
                <p style="text-align: center; color: #666; padding: 40px;">
                    🎨 您还没有生成任何角色<br>
                    <small>完成角色生成后，会在这里显示</small>
                </p>
            </div>
        </div>

        <!-- 动作库 -->
        <div class="container" id="motionsSection">
            <h2>🎬 动作库</h2>
            
            <div id="motionsStatus" class="status info">
                正在加载动作列表...
            </div>
            
            <div id="motionsGrid" class="motions-grid">
                <!-- 动作预览视频将动态插入这里 -->
            </div>
            
            <div id="motionsEmpty" class="empty-state hidden">
                <p style="text-align: center; color: #666; padding: 40px;">
                    🎭 暂无可用动作<br>
                    <small>动作库为空或加载失败</small>
                </p>
            </div>
        </div>

        <!-- 生成结果 -->
        <div class="container result-section" id="resultSection">
            <h2>📊 生成进度与结果</h2>
            
            <div id="taskInfo" class="task-status">
                等待任务创建...
            </div>
            
            <button id="stopPollingBtn" onclick="stopPolling()" class="hidden" style="background-color: #dc3545;">
                ⏹️ 停止监控
            </button>
            
            <!-- 下载区域 -->
            <div id="downloadArea" class="hidden" style="margin-top: 20px;">
                <h3>🎉 生成完成！</h3>
                <button id="downloadBtn" onclick="downloadModel()" style="background-color: #28a745;">
                    📥 下载3D模型
                </button>
                <p style="font-size: 14px; color: #666; margin-top: 10px;">
                    文件格式：GLB（支持Blender、Unity等软件导入）
                </p>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>