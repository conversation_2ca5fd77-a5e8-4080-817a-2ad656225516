/* Mokta Frontend Test - Main Styles */

/* 动作列表网格样式 */
.motions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.motion-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.motion-item:hover {
    background: #e9ecef;
    border-color: #28a745;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.motion-video {
    width: 100%;
    height: 120px;
    border-radius: 8px;
    object-fit: cover;
    margin-bottom: 12px;
    border: 2px solid #dee2e6;
    background-color: #e9ecef;
}

.motion-video:hover {
    border-color: #28a745;
}

.motion-name {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin: 0 0 8px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.motion-creator {
    font-size: 11px;
    color: #6c757d;
    margin: 0 0 8px 0;
}

.motion-date {
    font-size: 10px;
    color: #868e96;
    margin: 0;
}

.motion-download-hint {
    font-size: 10px;
    color: #28a745;
    margin-top: 8px;
    font-weight: 500;
}

/* 响应式：手机端优化 */
@media (max-width: 768px) {
    .motions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .motion-item {
        padding: 12px;
    }
    
    .motion-video {
        height: 100px;
    }
}

@media (max-width: 480px) {
    .motions-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .motion-video {
        height: 150px;
    }
}

/* 角色列表网格样式 */
.characters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.character-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.character-item:hover {
    background: #e9ecef;
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.character-thumbnail {
    width: 100%;
    height: 80px;
    object-fit: cover;
    border-radius: 6px;
    margin-bottom: 8px;
    border: 1px solid #dee2e6;
}

.character-name {
    font-size: 12px;
    color: #495057;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.character-date {
    font-size: 10px;
    color: #6c757d;
    margin: 4px 0 0 0;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

/* 响应式：手机端优化 */
@media (max-width: 480px) {
    .characters-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
    }
    
    .character-item {
        padding: 8px;
    }
    
    .character-thumbnail {
        height: 60px;
    }
}

/* 基础样式 */
body {
    font-family: Arial, sans-serif;
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f5f5;
}

.container {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

h1 {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
}

h2 {
    color: #555;
    border-bottom: 2px solid #eee;
    padding-bottom: 10px;
}

/* 状态样式 */
.status {
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #b7d4ea;
}

.warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* 按钮样式 */
button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 5px;
    cursor: pointer;
    margin: 10px 5px;
    font-size: 16px;
    min-width: 120px;
}

button:hover {
    background-color: #0056b3;
}

button:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

/* 文件上传区域 */
.upload-area {
    border: 2px dashed #ddd;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    margin: 20px 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.upload-area.dragover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.file-input {
    display: none;
}

.preview-image {
    max-width: 100%;
    max-height: 300px;
    margin: 20px 0;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 质量选择器 */
.quality-selector {
    margin: 20px 0;
}

.quality-selector label {
    display: block;
    margin-bottom: 10px;
    font-weight: bold;
}

.quality-selector select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
}

/* 进度条 */
.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin: 20px 0;
    display: none;
}

.progress-fill {
    height: 100%;
    background-color: #007bff;
    width: 0%;
    transition: width 0.3s ease;
}

/* 结果区域 */
.result-section {
    display: none;
    margin-top: 30px;
}

.task-status {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin: 15px 0;
    font-family: monospace;
}

/* 用户信息 */
.user-info {
    display: none;
    background-color: #e8f5e8;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

/* 通用类 */
.hidden {
    display: none;
}

/* 步骤指南 */
.step {
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 15px;
    margin: 10px 0;
    border-radius: 0 5px 5px 0;
}

.step-number {
    background-color: #007bff;
    color: white;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-weight: bold;
}

/* 下载区域 */
#downloadArea {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
}

#downloadArea h3 {
    color: #155724;
    margin-top: 0;
    margin-bottom: 15px;
}

#downloadBtn:hover {
    background-color: #218838 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* 配额显示样式 */
.quota-display {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.quota-display h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 16px;
}

.quota-progress {
    margin-bottom: 10px;
}

.quota-bar {
    width: 100%;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 8px;
}

.quota-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745 0%, #20c997 50%, #ffc107 80%, #dc3545 100%);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.quota-text {
    text-align: center;
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

.quota-status {
    text-align: center;
    font-size: 12px;
    padding: 5px;
    border-radius: 4px;
    margin-top: 8px;
}

.quota-status.available {
    background-color: #d4edda;
    color: #155724;
}

.quota-status.warning {
    background-color: #fff3cd;
    color: #856404;
}

.quota-status.exhausted {
    background-color: #f8d7da;
    color: #721c24;
}