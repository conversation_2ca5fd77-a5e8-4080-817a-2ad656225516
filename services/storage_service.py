import os
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any
from urllib.parse import urljoin

import boto3
from botocore.exceptions import ClientError
from minio import Minio
from minio.error import S3Error
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class StorageService:
    def __init__(self):
        self.use_minio = settings.USE_MINIO
        
        if self.use_minio:
            self.client = Minio(
                endpoint=settings.MINIO_ENDPOINT,
                access_key=settings.MINIO_ACCESS_KEY,
                secret_key=settings.MINIO_SECRET_KEY,
                secure=settings.MINIO_SECURE
            )
            self.bucket_name = settings.MINIO_BUCKET
        else:
            self.client = boto3.client(
                's3',
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_S3_REGION_NAME
            )
            self.bucket_name = settings.AWS_STORAGE_BUCKET_NAME
    
    def ensure_bucket_exists(self) -> bool:
        """确保存储桶存在"""
        try:
            if self.use_minio:
                if not self.client.bucket_exists(self.bucket_name):
                    self.client.make_bucket(self.bucket_name)
                    logger.info(f"Created MinIO bucket: {self.bucket_name}")
                return True
            else:
                self.client.head_bucket(Bucket=self.bucket_name)
                return True
        except Exception as e:
            logger.error(f"Error checking/creating bucket: {e}")
            return False
    
    def generate_presigned_upload_url(
        self, 
        file_name: str, 
        file_type: str, 
        expires_in: int = 3600
    ) -> Optional[Dict[str, Any]]:
        """生成预签名上传URL"""
        try:
            # 生成唯一的文件键
            file_extension = os.path.splitext(file_name)[1]
            file_key = f"uploads/{uuid.uuid4()}{file_extension}"
            
            if self.use_minio:
                # MinIO 预签名URL
                url = self.client.presigned_put_object(
                    bucket_name=self.bucket_name,
                    object_name=file_key,
                    expires=timedelta(seconds=expires_in)
                )
            else:
                # S3 预签名URL
                url = self.client.generate_presigned_url(
                    'put_object',
                    Params={
                        'Bucket': self.bucket_name,
                        'Key': file_key,
                        'ContentType': file_type
                    },
                    ExpiresIn=expires_in
                )
            
            return {
                'upload_url': url,
                'file_key': file_key,
                'expires_at': datetime.now() + timedelta(seconds=expires_in)
            }
        except Exception as e:
            logger.error(f"Error generating presigned URL: {e}")
            return None
    
    def generate_presigned_download_url(
        self, 
        file_key: str, 
        expires_in: int = 3600
    ) -> Optional[str]:
        """生成预签名下载URL"""
        try:
            if self.use_minio:
                url = self.client.presigned_get_object(
                    bucket_name=self.bucket_name,
                    object_name=file_key,
                    expires=timedelta(seconds=expires_in)
                )
            else:
                url = self.client.generate_presigned_url(
                    'get_object',
                    Params={
                        'Bucket': self.bucket_name,
                        'Key': file_key
                    },
                    ExpiresIn=expires_in
                )
            return url
        except Exception as e:
            logger.error(f"Error generating download URL: {e}")
            return None
    
    def upload_file(self, file_path: str, file_key: str) -> bool:
        """直接上传文件"""
        try:
            if self.use_minio:
                self.client.fput_object(
                    bucket_name=self.bucket_name,
                    object_name=file_key,
                    file_path=file_path
                )
            else:
                self.client.upload_file(
                    Filename=file_path,
                    Bucket=self.bucket_name,
                    Key=file_key
                )
            return True
        except Exception as e:
            logger.error(f"Error uploading file: {e}")
            return False
    
    def delete_file(self, file_key: str) -> bool:
        """删除文件"""
        try:
            if self.use_minio:
                self.client.remove_object(
                    bucket_name=self.bucket_name,
                    object_name=file_key
                )
            else:
                self.client.delete_object(
                    Bucket=self.bucket_name,
                    Key=file_key
                )
            return True
        except Exception as e:
            logger.error(f"Error deleting file: {e}")
            return False
    
    def file_exists(self, file_key: str) -> bool:
        """检查文件是否存在"""
        try:
            if self.use_minio:
                self.client.stat_object(
                    bucket_name=self.bucket_name,
                    object_name=file_key
                )
            else:
                self.client.head_object(
                    Bucket=self.bucket_name,
                    Key=file_key
                )
            return True
        except Exception:
            return False
    
    def get_file_url(self, file_key: str) -> str:
        """获取文件的公共URL（如果支持）"""
        if self.use_minio:
            if settings.MINIO_SECURE:
                protocol = "https"
            else:
                protocol = "http"
            return f"{protocol}://{settings.MINIO_ENDPOINT}/{self.bucket_name}/{file_key}"
        else:
            return f"https://{self.bucket_name}.s3.{settings.AWS_S3_REGION_NAME}.amazonaws.com/{file_key}"


# 单例实例
storage_service = StorageService()