# 🚀 Mokta Backend 部署指南

## 📋 部署清单

### **环境要求**
- [ ] **Python 3.11**: 强制要求版本
- [ ] **uv**: 现代 Python 包管理器
- [ ] **PostgreSQL Client**: 数据库连接工具

### **必需配置项**
- [ ] **SECRET_KEY**: Django 安全密钥（50字符以上）- 更新 `.env.production`
- [ ] **JWT_SECRET_KEY**: JWT 签名密钥（32字符以上）- 更新 `.env.production`
- [ ] **ALLOWED_HOSTS**: 服务器域名或IP地址 - 更新 `.env.production`
- [ ] **CORS_ALLOWED_ORIGINS**: 前端应用域名 - 更新 `.env.production`

### **服务依赖**
- [ ] PostgreSQL
- [ ] Redis (用于Celery队列和任务状态)
- [ ] MinIO (对象存储)
- [ ] GPU服务器 (独立部署，运行3D生成任务)

---

## 🚀 快速部署

### **1. 环境安装**
```bash
# 自动安装所有依赖，检查环境文件
./setup.sh
```

### **2. 启动服务**

**开发环境：**
```bash
./entrypoint.sh dev
# 自动启动：Django开发服务器 + Celery Worker（监听回调队列）
# 自动使用 .env.dev 配置
```

**生产环境：**
```bash
# 1. 生成并更新密钥到 .env.production
python -c "from django.core.management.utils import get_random_secret_key; print('SECRET_KEY=' + get_random_secret_key())"
python -c "import secrets; print('JWT_SECRET_KEY=' + secrets.token_urlsafe(32))"
nano .env.production

# 2. 启动生产服务器
./entrypoint.sh production
# 自动启动：Gunicorn服务器 + Celery Worker（监听回调队列）
# 自动使用 .env.production 配置
```

---

## ⚙️ 配置文件

项目包含两个环境配置文件：
- **`.env.production`**: 生产环境配置（使用 Kubernetes 服务地址）
- **`.env.dev`**: 开发环境配置（使用 localhost 地址）

**生产环境部署前需要更新的配置：**
```bash
SECRET_KEY=your-50-char-secret-key
JWT_SECRET_KEY=your-32-char-jwt-key
ALLOWED_HOSTS=your-server-ip,your-domain.com
CORS_ALLOWED_ORIGINS=https://your-frontend.com
```

### **环境文件自动管理**
- **生产模式**: 自动链接 `.env.production` → `.env`
- **开发模式**: 自动链接 `.env.dev` → `.env`
- **无需手动复制**: 启动脚本自动处理环境配置

| 文件 | 用途 | 数据库 | 服务地址 | DEBUG |
|------|------|--------|----------|-------|
| `.env.production` | 生产环境 | Kubernetes PostgreSQL | 内网服务地址 | False |
| `.env.dev` | 开发环境 | 本地 PostgreSQL | localhost 地址 | True |

---

## 🔧 脚本说明

### **setup.sh** - 统一环境设置
```bash
./setup.sh
```
**功能：**
- 安装系统依赖（Python 3.11, PostgreSQL client, Redis tools）
- 自动检测操作系统（macOS/Linux）
- 安装 uv 包管理器
- 创建 Python 3.11 虚拟环境
- 使用 uv 安装项目依赖（包含开发依赖）
- **检查环境配置文件**（验证 .env.production 和 .env.dev 都存在）
- 基础环境验证（Python 3.11, Django 安装）
- 显示密钥生成命令

### **entrypoint.sh** - 服务启动
```bash
./entrypoint.sh [production|dev]
```
**功能：**
- 验证 Python 3.11 环境
- **自动环境文件管理**（生产: 链接 .env.production，开发: 链接 .env.dev）
- 检查环境配置和密钥安全性
- 运行 Django 健康检查
- 数据库迁移和静态文件收集
- **启动Celery Worker**（监听callback队列，处理GPU服务器回调）
- 启动Django服务器（开发: Django runserver，生产: Gunicorn）
- 完整日志记录和进程管理（支持多进程优雅关闭）

---

## 📊 监控和管理

### **查看日志**
```bash
# 实时日志
tail -f logs/django.log              # Django服务器日志
tail -f logs/celery-worker.log       # Celery Worker日志
tail -f logs/gunicorn-error.log      # 生产模式Gunicorn日志

# 查看所有日志文件
ls -la logs/
# django.log, celery-worker.log, gunicorn-access.log, gunicorn-error.log
```

### **服务管理（生产模式）**
```bash
# 查看进程
ps aux | grep gunicorn               # Django进程
ps aux | grep celery                # Celery Worker进程

# 停止单个服务
kill $(cat logs/gunicorn.pid)       # 停止Django
kill $(cat logs/celery-worker.pid)  # 停止Worker

# 停止所有服务（推荐）
kill $(cat logs/gunicorn.pid) && kill $(cat logs/celery-worker.pid)

# 重启Django服务
kill -HUP $(cat logs/gunicorn.pid)

# 重启Worker（需要重新启动脚本）
kill $(cat logs/celery-worker.pid) && ./entrypoint.sh production
```

### **常用命令**
```bash
# 激活虚拟环境
source .venv/bin/activate

# Django 管理
python manage.py createsuperuser    # 创建管理员
python manage.py shell              # Django Shell
python manage.py check              # 配置检查
python manage.py migrate            # 运行迁移
```

---

## 🏗️ 分布式部署架构

### **双服务器架构**

```
┌─────────────────────────────────┐    ┌─────────────────────────────────┐
│         Django 服务器            │    │         GPU 服务器              │
│                                 │    │                                 │
│  ┌─────────────────────────────┐ │    │  ┌─────────────────────────────┐ │
│  │ Django + Gunicorn           │ │    │  │ Celery Worker               │ │
│  │ REST API 服务               │ │    │  │ (character_generation)      │ │
│  └─────────────────────────────┘ │    │  └─────────────────────────────┘ │
│  ┌─────────────────────────────┐ │    │  ┌─────────────────────────────┐ │
│  │ Celery Worker               │ │    │  │ GPU 深度学习模型            │ │
│  │ (callback队列)              │ │    │  │ 3D 生成计算                 │ │
│  └─────────────────────────────┘ │    │  └─────────────────────────────┘ │
└─────────────────────────────────┘    └─────────────────────────────────┘
            │                                         │
            └─────────── Redis队列通信 ──────────────┘
```

### **部署步骤**

#### **Django服务器（本项目）**
```bash
# 1. 部署Django服务器
./setup.sh
./entrypoint.sh production

# 服务包括：
# - Django REST API (端口8080)
# - Celery Worker (监听callback队列)
```

#### **GPU服务器（独立项目）**
```bash
# 2. 部署GPU服务器（使用celery_worker.py为参考）
# - 运行3D生成任务
# - 监听character_generation队列
# - 完成后发送回调到callback队列
```

#### **共享基础设施**
- **Redis**: 两个服务器共享同一Redis实例
- **PostgreSQL**: 只有Django服务器连接
- **MinIO**: 两个服务器共享同一对象存储

---

## 🚨 故障排除

### **常见问题**

1. **数据库连接失败**
   ```bash
   # 检查连接
   python manage.py check --database default
   ```

2. **OAuth 登录失败**
   - 检查 Google Console 授权域名
   - 验证 `GOOGLE_OAUTH_CLIENT_ID`

3. **CORS 错误**
   - 更新 `CORS_ALLOWED_ORIGINS`
   - 确保前端域名正确

4. **静态文件 404**
   ```bash
   python manage.py collectstatic --noinput
   ```

### **检查配置**
```bash
# Django 配置检查
python manage.py check

# 环境变量检查
grep -E "(SECRET_KEY|ALLOWED_HOSTS)" .env.production  # 生产环境
grep -E "(SECRET_KEY|ALLOWED_HOSTS)" .env.dev         # 开发环境

# 检查当前活动环境文件
ls -la .env                                          # 查看当前链接
```

---

## 📝 项目结构

```
mokta-backend/
├── setup.sh              # 环境设置脚本（Python 3.11 + uv）
├── entrypoint.sh          # 服务启动脚本（原生部署）
├── pyproject.toml         # Python 3.11 依赖配置
├── .env.production        # 生产环境配置
├── .env.dev               # 开发环境配置
├── .env                   # 当前环境链接（自动创建）
├── .venv/                 # Python 3.11 虚拟环境
├── mokta/                 # Django 项目
├── apps/                  # Django 应用
│   ├── authentication/   # OAuth 认证
│   ├── users/            # 用户管理
│   ├── characters/       # 3D 角色
│   ├── tasks/            # 任务管理
│   └── uploads/          # 文件上传
├── frontend-test/         # 前端测试页面
└── logs/                  # 日志目录
```