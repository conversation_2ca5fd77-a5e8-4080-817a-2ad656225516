# 常见问题记录
## 🔄 Celery 集成

### Celery应用名称不一致
**问题**: Django `mokta/celery.py` 使用 `Celery('mokta')`，而worker使用 `Celery('character_generator')`  
**解决**: 统一使用 `character_generator` 作为应用名称

### 任务路由配置错误
**问题**: 使用模块路径 `celery_tasks.character_generation.generate_3d_model` 但实际任务在另一台服务器  
**解决**: 改用直接任务名 `generate_3d_model` 和 `send_task()` 方法


## 🔄 前端集成
### MinIO CORS 配置问题
前端直接上传到 MinIO 遇到跨域错误，需要设置 `MINIO_SECURE=true` 启用 HTTPS。

### 前端页面未更新
