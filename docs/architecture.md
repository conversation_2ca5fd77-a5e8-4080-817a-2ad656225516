# 系统架构设计

## 🏗️ 整体架构概览

Mokta Backend 采用**分布式微服务架构**，将计算密集型任务与业务逻辑完全分离：

```
┌─────────────────────────────────────────────────────────────────────┐
│                           客户端层                                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐                  │
│  │   Web App   │  │ Mobile App  │  │  Admin UI   │                  │
│  └─────────────┘  └─────────────┘  └─────────────┘                  │
└─────────────────────────────────────────────────────────────────────┘
                                │
                        HTTPS / REST API
                                │
┌─────────────────────────────────────────────────────────────────────┐
│                        Django 服务器 (主服务器)                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐                  │
│  │   Django    │  │    DRF      │  │ Celery      │                  │
│  │   Core      │  │   REST API  │  │ Client      │                  │
│  └─────────────┘  └─────────────┘  └─────────────┘                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐                  │
│  │ Celery      │  │ Auth        │  │ File        │                  │
│  │ Worker      │  │ System      │  │ Management  │                  │
│  │(callback)   │  │ (OAuth)     │  │ (S3/MinIO)  │                  │
│  └─────────────┘  └─────────────┘  └─────────────┘                  │
└─────────────────────────────────────────────────────────────────────┘
                                │
                        Redis (队列 + 缓存)
                                │
┌─────────────────────────────────────────────────────────────────────┐
│                       GPU 计算服务器 (独立)                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐                  │
│  │   Celery    │  │    GPU      │  │   图像      │                  │
│  │   Worker    │  │   深度学习   │  │   处理      │                  │
│  │  (GPU任务)   │  │    模型     │  │   服务      │                  │
│  └─────────────┘  └─────────────┘  └─────────────┘                  │
└─────────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────────┐
│                          基础设施层                                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐                  │
│  │ PostgreSQL  │  │    Redis    │  │ MinIO/S3    │                  │
│  │  (主数据库)  │  │ (队列+缓存)  │  │ (对象存储)   │                  │
│  └─────────────┘  └─────────────┘  └─────────────┘                  │
└─────────────────────────────────────────────────────────────────────┘
```

## 🔧 组件选型与职责

| 组件 | 部署位置 | 主要职责 | 技术栈 |
|------|----------|----------|---------|
| **Django 主服务器** | 服务器 A | • 用户认证与会话管理<br>• REST API 提供<br>• 数据库 CRUD 操作<br>• 任务创建与状态查询<br>• 回调任务处理 | Django 5.2 + DRF + Gunicorn |
| **GPU 计算服务器** | 服务器 B | • 3D 模型生成计算<br>• 图像预处理<br>• 深度学习推理<br>• 重型计算任务 | Celery + PyTorch + CUDA |
| **Redis 集群** | 云服务/独立服务器 | • Celery 任务队列<br>• 任务状态缓存<br>• 会话存储 | Redis 5.0+ |
| **PostgreSQL** | 云服务/独立服务器 | • 业务数据持久化<br>• 用户信息管理<br>• 任务历史记录 | PostgreSQL 13+ |
| **对象存储** | 云服务 | • 用户上传文件存储<br>• 生成模型文件存储<br>• 静态资源托管 | MinIO/AWS S3 |

## 🔄 分布式任务流程

### 完整的 3D 角色生成流程

```mermaid
sequenceDiagram
    participant C as 客户端
    participant D as Django服务器
    participant R as Redis队列
    participant G as GPU服务器
    participant S as 对象存储
    participant DB as PostgreSQL

    C->>D: 1. 请求上传URL
    D->>S: 2. 生成预签名URL
    S-->>D: 3. 返回预签名URL
    D-->>C: 4. 返回上传URL
    
    C->>S: 5. 直接上传图片文件
    S-->>C: 6. 上传成功确认
    
    C->>D: 7. 创建生成任务
    D->>DB: 8. 保存任务记录
    D->>R: 9. 发送GPU任务到队列
    D-->>C: 10. 返回task_id
    
    G->>R: 11. 获取GPU任务
    G->>S: 12. 下载原始图片
    G->>G: 13. 图像处理+3D生成
    G->>S: 14. 上传生成的模型
    G->>R: 15. 发送回调任务
    
    D->>R: 16. 获取回调任务
    D->>DB: 17. 更新任务状态
    D->>DB: 18. 创建角色记录
    
    C->>D: 19. 轮询任务状态
    D->>R: 20. 查询任务状态
    D-->>C: 21. 返回完成状态+模型URL
```

### 队列设计

我们使用**双队列隔离**设计，确保不同类型的任务不会相互影响：

| 队列名称 | 处理者 | 任务类型 | 并发数 |
|----------|--------|----------|--------|
| `character_generation` | GPU服务器 | 3D模型生成计算 | 1-2 (GPU限制) |
| `callback` | Django服务器 | 数据库更新回调 | 2-4 (IO密集) |

## 🎭 应用分层架构

采用**六边形架构**（端口-适配器模式）和**领域驱动设计**原则：

### 📦 Django Apps 分层

```
┌─────────────────────────────────────────────────────────────────┐
│                        业务领域层 (Domain)                       │
│  ┌─────────────────┐   ┌─────────────────┐                      │
│  │   characters/   │   │    future/      │                      │
│  │   3D角色生成     │   │   扩展业务域     │                      │
│  │   ├─ models     │   │                 │                      │
│  │   ├─ services   │   │                 │                      │
│  │   └─ views      │   │                 │                      │
│  └─────────────────┘   └─────────────────┘                      │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                     基础设施层 (Infrastructure)                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │    auth/    │ │   users/    │ │  uploads/   │ │   tasks/    │ │
│  │  OAuth认证   │ │  用户管理    │ │  文件上传    │ │  任务管理    │ │
│  │  ├─ oauth   │ │  ├─ models  │ │  ├─ urls    │ │  ├─ models  │ │
│  │  ├─ jwt     │ │  ├─ admin   │ │  ├─ views   │ │  ├─ tasks   │ │
│  │  └─ views   │ │  └─ perms   │ │  └─ utils   │ │  └─ views   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                       服务层 (Services)                         │
│  ┌─────────────────┐   ┌─────────────────┐                      │
│  │ storage_service │   │ external_apis/  │                      │
│  │ 对象存储抽象     │   │ 第三方API集成    │                      │
│  └─────────────────┘   └─────────────────┘                      │
└─────────────────────────────────────────────────────────────────┘
```

### 🔧 职责边界

| 层级 | Apps | 特点 | 依赖方向 |
|------|------|------|----------|
| **业务领域层** | `characters/` | • 业务逻辑集中<br>• 领域模型<br>• 业务规则 | ↓ 依赖基础设施层 |
| **基础设施层** | `auth/`, `users/`, `uploads/`, `tasks/` | • 技术实现<br>• 可复用组件<br>• 外部集成 | ↓ 依赖服务层 |
| **服务层** | `services/` | • 跨领域服务<br>• 第三方集成<br>• 通用工具 | 无依赖 |

## 🔐 认证授权架构

### OAuth 2.0 + JWT 混合方案

```
┌─────────────────────────────────────────────────────────────────┐
│                        认证流程                                  │
│                                                                 │
│  Client ──1──> Google/Apple ──2──> ID Token                     │
│    │                                   │                       │
│    └──────────3─────────> Django ──4──┘                       │
│                            │                                   │
│                            ├─5─> 验证 ID Token                  │
│                            ├─6─> 获取用户信息                    │
│                            ├─7─> 查找/创建用户                   │
│                            └─8─> 签发 JWT Token                 │
│                                                                 │
│  Client <────9──── JWT Token (7天有效)                         │
│                                                                 │
│  后续API调用:                                                    │
│  Client ──Bearer JWT──> Django ──验证──> 业务逻辑               │
└─────────────────────────────────────────────────────────────────┘
```

### 用户权限模型

```python
# 动态权限计算
class User(AbstractUser):
    provider = models.CharField(max_length=20)  # 'google', 'apple', 'admin'
    
    @property
    def is_staff(self):
        return self.provider == 'admin'
    
    @property
    def is_superuser(self):
        return self.provider == 'admin'
```

#### 权限层级说明

| 用户类型 | provider值 | 权限范围 | 访问能力 |
|----------|------------|----------|----------|
| **普通用户** | `google`, `apple` | • API访问<br>• 角色生成<br>• 文件上传<br>• 查看自己的角色 | ❌ 无Admin访问权限 |
| **管理员** | `admin` | • 所有API访问<br>• Django Admin后台<br>• 动作库管理<br>• 用户管理<br>• 系统监控 | ✅ 完整管理权限 |

#### Django Admin 访问控制

- **访问路径**: `/admin/`
- **权限验证**: 仅 `provider='admin'` 的用户可访问
- **功能模块**:
  - 动作库管理 (`/admin/motions/motion/`)
  - 角色管理 (`/admin/characters/character/`)
  - 用户管理 (`/admin/users/user/`)
  - 任务监控 (`/admin/tasks/charactergenerationtask/`)

#### 管理员账户管理

```bash
# 创建管理员账户
python manage.py createsuperuser

# 或通过API创建admin用户
# 直接设置 provider='admin' 字段
```

## 🗄️ 数据库设计

### 核心表结构

#### 1. users - 用户表
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider VARCHAR(20) NOT NULL,
    provider_user_id VARCHAR(255) NOT NULL,
    email VARCHAR(254) UNIQUE NOT NULL,
    nickname VARCHAR(100),
    avatar_url TEXT,
    daily_generation_limit INTEGER DEFAULT 3,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(provider, provider_user_id)
);
```

#### 2. character_generation_tasks - 任务表
```sql
CREATE TABLE character_generation_tasks (
    id UUID PRIMARY KEY,  -- 与Celery task_id一致
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'PENDING',
    quality VARCHAR(10) DEFAULT 'high',
    source_photo_url TEXT NOT NULL,
    character_id UUID REFERENCES characters(id) ON DELETE SET NULL,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    
    CHECK (status IN ('PENDING', 'PROCESSING', 'SUCCESS', 'FAILED')),
    CHECK (quality IN ('high', 'medium', 'low', 'flux', 'test'))
);
```

#### 3. characters - 角色表
```sql
CREATE TABLE characters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    model_url TEXT NOT NULL,
    thumbnail_url TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### 4. motions - 动作表
```sql
CREATE TABLE motions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    creator_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    motion_url TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 索引策略

```sql
-- 用户查询优化
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_provider ON users(provider, provider_user_id);

-- 任务查询优化
CREATE INDEX idx_tasks_user_status ON character_generation_tasks(user_id, status);
CREATE INDEX idx_tasks_created_at ON character_generation_tasks(created_at DESC);
CREATE INDEX idx_tasks_user_date ON character_generation_tasks(user_id, DATE(created_at));

-- 角色查询优化
CREATE INDEX idx_characters_owner ON characters(owner_id, created_at DESC);

-- 动作查询优化
CREATE INDEX idx_motions_creator ON motions(creator_id, created_at DESC);
CREATE INDEX idx_motions_created_at ON motions(created_at DESC);
```

## 🚀 部署架构

### 生产环境部署拓扑

```
┌─────────────────────────────────────────────────────────────────┐
│                          负载均衡器                              │
│                     (Nginx/CloudFlare)                         │
└─────────────────────────────────────────────────────────────────┘
                                │
                    ┌───────────┴───────────┐
                    │                       │
┌─────────────────────────────┐  ┌─────────────────────────────┐
│      Django 服务器集群       │  │      GPU 计算服务器集群      │
│                             │  │                             │
│  ┌─────────────────────────┐ │  │  ┌─────────────────────────┐ │
│  │ Django + Gunicorn       │ │  │  │ Celery Worker + GPU     │ │
│  │ Celery Worker (回调)    │ │  │  │ 深度学习模型            │ │
│  └─────────────────────────┘ │  │  └─────────────────────────┘ │
│  ┌─────────────────────────┐ │  │  ┌─────────────────────────┐ │
│  │ Django + Gunicorn       │ │  │  │ Celery Worker + GPU     │ │
│  │ Celery Worker (回调)    │ │  │  │ 深度学习模型            │ │
│  └─────────────────────────┘ │  │  └─────────────────────────┘ │
└─────────────────────────────┘  └─────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                         基础设施层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ PostgreSQL  │ │Redis Cluster│ │   MinIO     │ │ Monitoring  │ │
│  │   主从复制   │ │   分片集群   │ │  对象存储    │ │ Prometheus  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 容错与高可用

1. **服务隔离**: Django 和 GPU 计算完全分离
2. **队列冗余**: Redis 集群部署，支持故障转移
3. **数据备份**: PostgreSQL 主从复制 + 定时备份
4. **任务重试**: 自动重试机制处理临时故障
5. **监控告警**: 全链路监控和异常告警

## 📈 性能与扩展性

### 性能指标

| 组件 | 目标指标 | 扩展方式 |
|------|----------|----------|
| Django API | < 200ms 响应时间 | 水平扩展 + 负载均衡 |
| 文件上传 | < 5s (10MB) | CDN + 预签名URL |
| 3D生成任务 | 2-10分钟 | GPU 服务器水平扩展 |
| 数据库查询 | < 50ms | 读写分离 + 查询优化 |

### 扩展策略

1. **Django 层**: 无状态设计，支持水平扩展
2. **GPU 层**: 按需增加 GPU 服务器
3. **存储层**: 对象存储自动扩展
4. **缓存层**: Redis 分片扩展