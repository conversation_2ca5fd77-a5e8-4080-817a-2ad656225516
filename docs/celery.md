# Celery 集成规范

本文档定义了 Django 服务器与 Celery Worker 服务器之间的集成规范，确保两个项目的配置保持一致。

## 🏗️ 架构概览

```
Django 服务器 (Celery 客户端)     Celery Worker 服务器 (任务执行者)
├── mokta/celery.py              ├── celery_worker.py
├── 创建任务                      ├── 执行任务
├── 查询任务状态                  ├── 报告任务进度
└── 管理任务生命周期              └── 返回任务结果
```

## 📋 配置规范

### **Celery 应用名称**
**必须保持一致！**

- **Django 服务器**: `Celery('character_generator')`
- **Celery Worker 服务器**: `Celery('character_generator')`

### **Redis 连接配置**
- **Broker URL**: `redis://user:pass@host:port/db`
- **Result Backend**: `redis://user:pass@host:port/db`

### **队列配置**
- **默认队列**: `character_generation`
- **任务路由**: 所有 3D 生成任务路由到此队列

## 🔧 任务定义规范

### **队列设计**

| 队列名称 | 处理者 | 任务类型 | 并发数 | 描述 |
|----------|--------|----------|--------|------|
| `character_generation` | GPU服务器 | 3D模型生成计算 | 1-2 | 计算密集型任务 |
| `callback` | Django服务器 | 数据库更新回调 | 2-4 | IO密集型任务 |

### **任务名称和签名**

#### `generate_3d_model`
**功能**: 从 2D 照片生成 3D 模型

**参数**:
```python
def generate_3d_model(user_id, photo_object_name, quality="high"):
    """
    Args:
        user_id (str): 用户ID (UUID字符串)
        photo_object_name (str): 照片在对象存储中的对象名称
        quality (str): 生成质量 ("high", "medium", "low", "flux", "test")
    
    Returns:
        dict: {
            "status": "success|failed",
            "model_url": "https://...",  # 成功时
            "error": "error message"     # 失败时
        }
    """
```

**Django 调用方式**:
```python
from mokta.celery import app as celery_app

task = celery_app.send_task(
    'generate_3d_model',
    args=[user_id, photo_object_name, quality],
    queue='character_generation'
)
```

**Celery Worker 定义**:
```python
@shared_task(bind=True, name='generate_3d_model', queue='character_generation', max_retries=2, default_retry_delay=300)
def generate_3d_model(self, user_id, photo_object_name, quality="high"):
    # 实际执行逻辑
    pass
```

#### `update_task_result`
**功能**: 更新Django数据库中的任务状态（GPU服务器回调）

**参数**:
```python
def update_task_result(task_id, status, result_data=None, error_message=None):
    """
    Args:
        task_id (str): 任务ID (UUID字符串)
        status (str): 任务状态 ("SUCCESS" | "FAILURE")
        result_data (dict): 成功时的结果数据 {"status": "success", "model_url": "..."}
        error_message (str): 失败时的错误信息
    
    Returns:
        None
    """
```

**Django端定义**:
```python
@shared_task(bind=True, name='update_task_result', max_retries=3, default_retry_delay=60)
def update_task_result(self, task_id, status, result_data=None, error_message=None):
    # 更新数据库状态，创建角色记录
    pass
```

## 📊 任务状态规范

### **状态流转**
```
PENDING → PROCESSING → SUCCESS/FAILURE
                  ↓
                RETRY → PROCESSING → SUCCESS/FAILURE
```

### **状态定义**

#### `PENDING`
- **含义**: 任务已创建，等待执行
- **Django 响应**: 
```json
{
    "task_id": "uuid",
    "status": "PENDING",
    "progress": 0,
    "message": "Task is waiting to be processed"
}
```

#### `PROCESSING`
- **含义**: 任务正在执行中
- **Worker 更新**:
```python
self.update_state(
    state='PROCESSING',
    meta={
        'status': 'processing_img|processing_3d|processing_remesh|processing_rigging|uploading',
        'message': '正在处理图片...',
        'progress': 25  # 0-100
    }
)
```
- **Django 响应**:
```json
{
    "task_id": "uuid",
    "status": "PROCESSING",
    "progress": 25,
    "message": "正在处理图片...",
    "current_step": "processing_img"
}
```

#### `RETRY`
- **含义**: 任务执行失败，正在重试
- **Worker 更新**:
```python
self.update_state(
    state='RETRY',
    meta={
        'status': 'retrying',
        'message': f'重试中... ({self.request.retries + 1}/{self.max_retries})',
        'progress': 0,
        'error': str(e),
        'retry_count': self.request.retries + 1
    }
)
```
- **Django 响应**:
```json
{
    "task_id": "uuid",
    "status": "RETRY",
    "progress": 0,
    "message": "重试中... (2/3)",
    "retry_count": 2
}
```

#### `SUCCESS`
- **含义**: 任务成功完成
- **Worker 更新**:
```python
self.update_state(
    state='SUCCESS',
    meta={
        'status': 'completed',
        'message': '处理完成',
        'progress': 100,
        'model_url': 'https://minio.../3d_models/user_id/model.glb'
    }
)
```
- **Django 响应**:
```json
{
    "task_id": "uuid",
    "status": "SUCCESS",
    "progress": 100,
    "message": "处理完成",
    "model_url": "https://minio.../3d_models/user_id/model.glb"
}
```

#### `FAILURE`
- **含义**: 任务执行失败
- **Worker 更新**:
```python
self.update_state(
    state='FAILURE',
    meta={
        'status': 'failed',
        'message': f'处理失败: {str(e)}',
        'progress': 0,
        'error': str(e)
    }
)
```
- **Django 响应**:
```json
{
    "task_id": "uuid",
    "status": "FAILED",
    "error_message": "处理失败: connection timeout",
    "error": "connection timeout",
    "progress": 0
}
```

## 🔄 处理流程规范

### **3D 模型生成流程**
Celery Worker 执行的标准流程：

1. **图片下载** (5%)
   - 从 MinIO 下载用户上传的照片
   - 状态: `processing_img`

2. **图片预处理** (25%)
   - 调用商用API处理图片
   - 生成 T-pose 图片

3. **3D 模型生成** (65%)
   - 调用 GPU 服务生成基础 3D 模型
   - 状态: `processing_3d`

4. **模型重拓扑** (80%)
   - 优化模型拓扑结构
   - 状态: `processing_remesh`

5. **骨骼绑定** (85%)
   - 为模型绑定骨骼系统
   - 状态: `processing_rigging`

6. **结果上传** (95%)
   - 上传最终模型到 MinIO
   - 状态: `uploading`

7. **完成** (100%)
   - 返回模型 URL
   - 状态: `completed`



## 🚨 注意事项

### **1. 应用名称一致性**
- **关键**: Django 和 Celery Worker 必须使用相同的 Celery 应用名称 `character_generator`
- **后果**: 不一致会导致任务无法正确路由

### **2. Redis 连接配置**
- **要求**: 两端必须连接到同一个 Redis 实例
- **参数**: Broker 和 Result Backend 的连接字符串必须一致

### **3. 任务参数类型**
- **user_id**: 必须是字符串类型 (UUID.hex)
- **photo_object_name**: MinIO 对象完整路径
- **quality**: 只接受 "high", "medium", "low", "flux", "test"

### **4. 错误处理**
- **超时**: 任务总超时 30 分钟
- **重试**: 根据具体错误类型决定是否重试
- **日志**: 记录详细的错误信息用于调试

### **5. 对象存储路径规范**
- **输入照片**: `uploads/user_id/photo.jpg`
- **输出模型**: `3d_models/user_id/model.glb`

### **6. Django端启动配置**
- **自动启动**: Django服务器会自动启动Celery Worker监听`callback`队列
- **启动脚本**: `./entrypoint.sh` 会同时启动Django服务器和Celery Worker
- **日志文件**: 
  - Django日志: `logs/django.log`
  - Worker日志: `logs/celery-worker.log`
- **进程管理**: 支持优雅关闭，Ctrl+C会同时停止所有服务

### **7. 重试机制**
- **GPU任务**: 最多重试2次，延迟300秒
- **回调任务**: 最多重试3次，延迟60秒  
- **重试条件**: 网络错误、连接超时等可恢复错误
- **不重试**: 业务逻辑错误、参数错误等

