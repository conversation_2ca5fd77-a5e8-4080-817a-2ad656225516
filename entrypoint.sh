#!/bin/bash

# Mokta Backend 启动脚本
# 支持 production 和 dev 模式

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查参数
MODE=${1:-dev}
if [[ "$MODE" != "production" && "$MODE" != "dev" ]]; then
    log_error "无效模式: $MODE"
    echo "使用方法: ./entrypoint.sh [production|dev]"
    exit 1
fi

log_info "启动 Mokta Backend ($MODE 模式)..."

# 检查虚拟环境
check_venv() {
    if [[ ! -d ".venv" ]]; then
        log_error "虚拟环境不存在，请先运行: ./setup.sh $MODE"
        exit 1
    fi
}

# 激活虚拟环境
activate_venv() {
    log_info "激活虚拟环境..."
    source .venv/bin/activate
}

# 自动设置环境文件
setup_env_file() {
    if [[ "$MODE" == "production" ]]; then
        log_info "使用生产环境配置..."
        
        if [[ ! -f ".env.production" ]]; then
            log_error "生产环境配置文件 .env.production 不存在"
            exit 1
        fi
        
        # 创建符号链接到生产环境配置
        rm -f .env
        ln -sf .env.production .env
        log_success "已链接生产环境配置文件: .env.production → .env"
        
    else
        log_info "使用开发环境配置..."
        
        if [[ ! -f ".env.dev" ]]; then
            log_error "开发环境配置文件 .env.dev 不存在"
            exit 1
        fi
        
        # 创建符号链接到开发环境配置
        rm -f .env
        ln -sf .env.dev .env
        log_success "已链接开发环境配置文件: .env.dev → .env"
    fi
}

# 检查环境变量
check_env() {
    if [[ ! -f ".env" ]]; then
        log_error ".env 文件不存在"
        exit 1
    fi
    
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    if python manage.py migrate; then
        log_success "数据库迁移完成"
    else
        log_warning "数据库迁移失败，将继续启动服务器"
    fi
}

# 收集静态文件（生产模式）
collect_static() {
    if [[ "$MODE" == "production" ]]; then
        log_info "收集静态文件..."
        python manage.py collectstatic --noinput --clear
        log_success "静态文件收集完成"
    fi
}

# 创建日志目录
setup_logging() {
    mkdir -p logs
    
    if [[ "$MODE" == "production" ]]; then
        # 生产环境日志文件
        touch logs/django.log
        touch logs/gunicorn-access.log
        touch logs/gunicorn-error.log
        touch logs/celery-worker.log
    else
        # 开发环境日志文件
        touch logs/django.log
        touch logs/celery-worker.log
    fi
}

# 运行健康检查
health_check() {
    log_info "运行健康检查..."
    
    if python manage.py check; then
        log_success "Django 配置检查通过"
    else
        log_error "Django 配置检查失败"
        exit 1
    fi
}

# 启动 Celery Worker（用于接收GPU服务器的回调任务）
start_celery_worker() {
    log_info "启动 Celery Worker..."
    
    # 停止可能存在的worker进程
    if [[ -f "logs/celery-worker.pid" ]]; then
        local pid=$(cat logs/celery-worker.pid)
        if kill -0 $pid 2>/dev/null; then
            log_info "停止现有的 Celery Worker 进程 (PID: $pid)..."
            kill -TERM $pid
            sleep 2
        fi
        rm -f logs/celery-worker.pid
    fi
    
    # 启动worker，监听callback队列用于接收GPU服务器的回调任务
    nohup celery -A mokta worker \
        --loglevel=info \
        --queues=callback \
        --concurrency=2 \
        --logfile=logs/celery-worker.log \
        --pidfile=logs/celery-worker.pid \
        --hostname=django-callback-worker@%h \
        > logs/celery-worker.log 2>&1 &
    
    # 等待worker启动
    sleep 3
    
    if [[ -f "logs/celery-worker.pid" ]] && kill -0 $(cat logs/celery-worker.pid) 2>/dev/null; then
        log_success "Celery Worker 启动成功 (PID: $(cat logs/celery-worker.pid))"
    else
        log_error "Celery Worker 启动失败"
        exit 1
    fi
}

# 启动开发服务器
start_dev_server() {
    log_info "启动开发服务器..."
    
    echo ""
    echo "================================"
    echo "🚀 Mokta Backend 开发服务器"
    echo "🌐 服务器地址: http://localhost:8080"
    echo "👤 管理后台: http://localhost:8080/admin/"
    echo "🧪 API 测试: frontend-test/index.html"
    echo "📋 Django日志: logs/django.log"
    echo "📋 Worker日志: logs/celery-worker.log"
    echo "🔄 Celery Worker: 监听callback队列"
    echo ""
    echo "按 Ctrl+C 停止所有服务"
    echo "================================"
    echo ""
    
    # 启动服务器并记录日志
    python manage.py runserver 0.0.0.0:8080 2>&1 | tee logs/django.log
}

# 启动生产服务器
start_production_server() {
    log_info "启动生产服务器..."
    
    # 检查 gunicorn 是否安装
    if ! command -v gunicorn &> /dev/null; then
        log_error "gunicorn 未安装，请运行: uv add gunicorn"
        exit 1
    fi
    
    # 停止可能存在的服务器进程
    if [[ -f "logs/gunicorn.pid" ]]; then
        local pid=$(cat logs/gunicorn.pid)
        if kill -0 $pid 2>/dev/null; then
            log_info "停止现有的 gunicorn 进程 (PID: $pid)..."
            kill -TERM $pid
            sleep 2
        fi
        rm -f logs/gunicorn.pid
    fi
    
    echo ""
    echo "================================"
    echo "🚀 Mokta Backend 生产服务器"
    echo "🌐 服务器地址: http://localhost:8080"
    echo "👤 管理后台: http://localhost:8080/admin/"
    echo "📋 访问日志: logs/gunicorn-access.log"
    echo "📋 错误日志: logs/gunicorn-error.log"
    echo "📋 Worker日志: logs/celery-worker.log"
    echo "🆔 Gunicorn PID: logs/gunicorn.pid"
    echo "🆔 Worker PID: logs/celery-worker.pid"
    echo "🔄 Celery Worker: 监听callback队列"
    echo ""
    echo "使用以下命令管理服务:"
    echo "  查看Django日志: tail -f logs/gunicorn-error.log"
    echo "  查看Worker日志: tail -f logs/celery-worker.log"
    echo "  停止Django: kill \$(cat logs/gunicorn.pid)"
    echo "  停止Worker: kill \$(cat logs/celery-worker.pid)"
    echo "  重启Django: kill -HUP \$(cat logs/gunicorn.pid)"
    echo "================================"
    echo ""
    
    # 启动 gunicorn
    exec gunicorn mokta.wsgi:application \
        --bind 0.0.0.0:8080 \
        --workers 3 \
        --worker-class sync \
        --worker-connections 1000 \
        --max-requests 1000 \
        --max-requests-jitter 100 \
        --timeout 30 \
        --keep-alive 2 \
        --log-level info \
        --access-logfile logs/gunicorn-access.log \
        --error-logfile logs/gunicorn-error.log \
        --capture-output \
        --pid logs/gunicorn.pid
}

# 显示服务器信息
show_server_info() {
    log_success "🎉 服务器准备就绪！"
    
    echo ""
    echo "📊 服务器信息:"
    echo "  模式: $MODE"
    echo "  Python: $(python --version)"
    echo "  Django: $(python -c 'import django; print(django.get_version())')"
    echo "  工作目录: $(pwd)"
    echo ""
    
    if [[ "$MODE" == "dev" ]]; then
        echo "🔧 开发工具:"
        echo "  创建超级用户: python manage.py createsuperuser"
        echo "  Django Shell: python manage.py shell"
        echo "  运行测试: python manage.py test"
        echo "  API 测试页面: frontend-test/index.html"
        echo "  查看Worker日志: tail -f logs/celery-worker.log"
        echo "  重启Worker: kill \$(cat logs/celery-worker.pid) && ./entrypoint.sh dev"
    else
        echo "🔧 生产工具:"
        echo "  查看Django进程: ps aux | grep gunicorn"
        echo "  查看Worker进程: ps aux | grep celery"
        echo "  重启Django: kill -HUP \$(cat logs/gunicorn.pid)"
        echo "  重启Worker: kill \$(cat logs/celery-worker.pid) && ./entrypoint.sh production"
        echo "  停止所有服务: kill \$(cat logs/gunicorn.pid) && kill \$(cat logs/celery-worker.pid)"
    fi
}

# 停止所有服务
stop_all_services() {
    log_info "停止所有服务..."
    
    # 停止Celery Worker
    if [[ -f "logs/celery-worker.pid" ]]; then
        local worker_pid=$(cat logs/celery-worker.pid)
        if kill -0 $worker_pid 2>/dev/null; then
            log_info "停止 Celery Worker (PID: $worker_pid)..."
            kill -TERM $worker_pid
            # 等待优雅关闭
            sleep 2
            # 如果还没停止，强制关闭
            if kill -0 $worker_pid 2>/dev/null; then
                kill -KILL $worker_pid
            fi
        fi
        rm -f logs/celery-worker.pid
    fi
    
    # 停止Gunicorn（生产模式）
    if [[ "$MODE" == "production" && -f "logs/gunicorn.pid" ]]; then
        local gunicorn_pid=$(cat logs/gunicorn.pid)
        if kill -0 $gunicorn_pid 2>/dev/null; then
            log_info "停止 Gunicorn (PID: $gunicorn_pid)..."
            kill -TERM $gunicorn_pid
        fi
        rm -f logs/gunicorn.pid
    fi
    
    log_success "所有服务已停止"
}

# 信号处理
setup_signal_handlers() {
    if [[ "$MODE" == "dev" ]]; then
        trap 'echo -e "\n👋 停止开发服务器..."; stop_all_services; exit 0' SIGINT SIGTERM
    else
        trap 'stop_all_services; exit 0' SIGINT SIGTERM
    fi
}

# 主函数
main() {
    check_venv
    activate_venv
    setup_env_file
    check_env
    setup_logging
    health_check
    run_migrations
    collect_static
    start_celery_worker  # 启动Celery Worker接收GPU服务器回调
    show_server_info
    setup_signal_handlers
    
    if [[ "$MODE" == "dev" ]]; then
        start_dev_server
    else
        start_production_server
    fi
}

# 执行主函数
main