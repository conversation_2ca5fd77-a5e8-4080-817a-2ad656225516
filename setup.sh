#!/bin/bash

# Mokta Backend 环境设置脚本
# 适用于开发和生产环境

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

log_info "设置 Mokta Backend 环境..."

# 安装系统依赖
install_system_deps() {
    log_info "安装系统依赖..."
    
    # 检查是否为root用户
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要以root用户运行此脚本"
        exit 1
    fi
    
    # 检查sudo权限
    if ! sudo -n true 2>/dev/null; then
        log_info "需要sudo权限安装系统依赖..."
    fi
    
    # 更新包管理器
    log_info "更新包管理器..."
    sudo apt update
    
    # 安装必要的系统包
    log_info "安装Python 3.11和相关工具..."
    sudo apt install -y git curl postgresql-client redis-tools
    
    # 验证关键工具安装
    if ! command -v python3.11 &> /dev/null; then
        log_error "Python 3.11 安装失败"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        log_error "Git 安装失败"
        exit 1
    fi
    
    log_success "系统依赖安装完成"
}

# 安装 uv
install_uv() {
    if ! command -v uv &> /dev/null; then
        log_info "安装 uv..."
        
        # 确保HOME目录存在
        if [[ -z "$HOME" ]]; then
            log_error "HOME 环境变量未设置"
            exit 1
        fi
        
        # 安装uv
        curl -LsSf https://astral.sh/uv/install.sh | sh
        
        # 检查安装是否成功
        if [[ -f "$HOME/.local/bin/uv" ]]; then
            # 添加到PATH
            export PATH="$HOME/.local/bin:$PATH"
            log_success "uv 安装完成"
        else
            log_error "uv 安装失败"
            exit 1
        fi
    else
        log_info "uv 已安装: $(uv --version)"
    fi
}

# 设置 Python 环境
setup_python_env() {
    log_info "设置 Python 虚拟环境..."
    
    # 创建虚拟环境
    uv venv --python 3.11
    
    # 激活虚拟环境
    source .venv/bin/activate
    
    # 安装依赖（包含开发依赖，适用于两种环境）
    log_info "安装项目依赖..."
    uv sync --extra dev
    
    log_success "Python 环境设置完成"
}

# 检查环境配置文件
check_env_config() {
    log_info "检查环境配置文件..."
    
    # 检查生产环境配置
    if [[ ! -f ".env.production" ]]; then
        log_error "生产环境配置文件 .env.production 不存在"
        exit 1
    fi
    log_success "生产环境配置文件检查通过: .env.production"
    
    # 检查开发环境配置
    if [[ ! -f ".env.dev" ]]; then
        log_error "开发环境配置文件 .env.dev 不存在"
        exit 1
    fi
    log_success "开发环境配置文件检查通过: .env.dev"
}

# 数据库设置
setup_database() {
    log_info "准备数据库配置..."
    
    source .venv/bin/activate
    
    # 解析数据库URL（优先.env.dev，其次.env.production）
    if [[ -f ".env.dev" ]]; then
        db_url=$(grep '^DATABASE_URL=' .env.dev | cut -d'=' -f2-)
    elif [[ -f ".env.production" ]]; then
        db_url=$(grep '^DATABASE_URL=' .env.production | cut -d'=' -f2-)
    else
        log_error ".env.dev 和 .env.production 都不存在，无法获取数据库配置"
        exit 1
    fi
    
    if [[ -z "$db_url" ]]; then
        log_error "未找到DATABASE_URL配置"
        exit 1
    fi
    
    # 提取数据库名
    db_name=$(echo "$db_url" | sed -E 's#.*/##')
    db_url_no_db=$(echo "$db_url" | sed -E 's#(.*//[^/]+):[^/]+/(.*)#\1/postgres#')
    
    # 检查数据库是否存在
    if PGPASSWORD=$(echo "$db_url" | sed -E 's#postgresql://[^:]+:([^@]+)@.*#\1#') psql "$db_url" -c "\q" 2>/dev/null; then
        log_info "数据库 $db_name 已存在，无需创建"
    else
        log_info "数据库 $db_name 不存在，正在尝试创建..."
        # 提取用户名和密码
        db_user=$(echo "$db_url" | sed -E 's#postgresql://([^:]+):.*#\1#')
        db_pass=$(echo "$db_url" | sed -E 's#postgresql://[^:]+:([^@]+)@.*#\1#')
        db_host=$(echo "$db_url" | sed -E 's#postgresql://[^@]+@([^:/]+).*#\1#')
        db_port=$(echo "$db_url" | sed -E 's#postgresql://[^@]+@[^:/]+:([0-9]+).*#\1#')
        if [[ -z "$db_port" ]]; then db_port=5432; fi
        PGPASSWORD="$db_pass" psql -h "$db_host" -U "$db_user" -p "$db_port" -d postgres -c "CREATE DATABASE $db_name;" && \
        log_success "数据库 $db_name 创建成功" || \
        log_warning "数据库 $db_name 创建失败，可能已存在或权限不足"
    fi
    
    # 数据库迁移将在服务启动时进行
    log_info "数据库迁移将在服务启动时自动执行"
    log_info "启动开发服务后可运行: python manage.py createsuperuser"
}

# 密钥生成提示
show_key_generation() {
    log_info "生产环境密钥生成命令:"
    echo "================================"
    echo "python -c \"from django.core.management.utils import get_random_secret_key; print('SECRET_KEY=' + get_random_secret_key())\""
    echo "python -c \"import secrets; print('JWT_SECRET_KEY=' + secrets.token_urlsafe(32))\""
    echo "================================"
    log_info "生产部署前请使用上述命令更新 .env.production 中的密钥"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p logs
    mkdir -p static
    mkdir -p media
    
    log_success "目录创建完成"
}

# 运行基础检查
run_checks() {
    log_info "运行基础检查..."
    
    source .venv/bin/activate
    
    # 检查Python版本
    python_version=$(python --version 2>&1)
    if [[ "$python_version" == *"3.11"* ]]; then
        log_success "Python 版本检查通过: $python_version"
    else
        log_error "Python 版本不匹配，需要 Python 3.11"
        return 1
    fi
    
    # 检查Django安装
    if python -c "import django; print('Django', django.get_version())" 2>/dev/null; then
        log_success "Django 安装检查通过"
    else
        log_error "Django 安装检查失败"
        return 1
    fi
    
    log_info "详细配置检查将在服务启动时进行"
}

# 显示完成信息
show_completion_info() {
    log_success "🎉 Mokta Backend 设置完成！"
    echo ""
    echo "================================"
    echo "🐍 Python: $(python --version 2>/dev/null || echo "未激活虚拟环境")"
    echo "📂 项目目录: $(pwd)"
    echo "📄 环境文件: .env.dev, .env.production"
    echo "================================"
    echo ""
    echo "🚀 下一步:"
    echo "  开发环境:"
    echo "    ./entrypoint.sh dev"
    echo "    环境配置将自动链接: .env.dev → .env"
    echo ""
    echo "  生产环境:"
    echo "    1. 更新 .env.production 中的密钥配置"
    echo "    2. 启动: ./entrypoint.sh production"
    echo "    3. 环境配置将自动链接: .env.production → .env"
    echo ""
    echo "📚 更多信息请查看 DEPLOYMENT.md"
}

# 主函数
main() {
    install_system_deps
    install_uv
    setup_python_env
    check_env_config
    create_directories
    setup_database
    show_key_generation
    run_checks
    show_completion_info
}

# 执行主函数
main

