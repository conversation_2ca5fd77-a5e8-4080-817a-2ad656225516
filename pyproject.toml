[project]
name = "mokta-backend"
version = "1.0.0"
description = "Mokta Backend - 2D Photo to 3D Character Generation API"
authors = [
    {name = "Mokta Team"}
]
readme = "README.md"
requires-python = "==3.11.*"
dependencies = [
    "django==5.2",
    "djangorestframework>=3.14.0",
    "django-cors-headers>=4.3.1",
    "celery>=5.3.4",
    "redis>=5.0.1",
    "psycopg2-binary>=2.9.9",
    "PyJWT>=2.8.0",
    "cryptography>=41.0.0",
    "requests>=2.31.0",
    "google-auth>=2.25.2",
    "python-jose[cryptography]>=3.3.0",
    "python-multipart>=0.0.6",
    "boto3>=1.34.0",
    "minio>=7.1.0",
    "Pillow>=10.1.0",
    "pytz>=2023.3",
    "gunicorn>=21.2.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-django>=4.5.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "isort>=5.12.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_django = "django"
sections = ["FUTURE", "STDLIB", "DJANGO", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "migrations",
    ".venv",
    "build",
    "dist"
]

# 添加hatchling构建配置
[tool.hatch.build.targets.wheel]
packages = ["mokta", "apps", "services", "celery_tasks"]