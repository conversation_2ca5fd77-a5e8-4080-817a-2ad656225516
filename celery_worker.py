import os
import asyncio
import logging
import requests
import random
import glob
import shutil
from minio import Minio
from celery import Celery, shared_task
from celery.signals import task_success, task_failure
from app.config import config
from app.api.openai_image import edit_image
from app.api.rodin_gen import generate
from app.api.remesh import remesh
from app.api.rigging import rigging
import aiofiles


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# MinIO 客户端
minio_client = Minio(
    config.MINIO_ENDPOINT,
    access_key=config.MINIO_ACCESS_KEY,
    secret_key=config.MINIO_SECRET_KEY,
    secure=config.MINIO_SECURE
)

# Celery 配置
celery_worker = Celery(
    'character_generator',
    broker=config.CELERY_BROKER_URL,
    backend=config.CELERY_RESULT_BACKEND
)

# Celery 日志配置
celery_worker.conf.update(
    worker_log_format='[%(asctime)s: %(levelname)s/%(processName)s] %(message)s',
    worker_task_log_format='[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s',
    worker_log_color=True,
    task_always_eager=False,
    task_eager_propagates=True,
    # Beat 配置
    beat_schedule={
        'ssh-forward-every-60-seconds': {
            'task': 'ssh_forward_check',
            'schedule': 60.0,  # 每60秒执行一次
        },
        'cleanup-temp-files-every-hour': {
            'task': 'cleanup_temp_files_task',
            'schedule': 3600.0,  # 每小时执行一次
        },
    },
)

@shared_task(bind=True, name='generate_3d_model', queue='character_generation')
def generate_3d_model(self, user_id, photo_object_name, quality="high"):
    """
    1. 从 MinIO 下载图片
    2. 调用商用API处理图片
    3. 调用自有GPU服务生成3D模型
    4. 上传最终模型到MinIO
    5. 返回结果
    """
    logger.info(f"开始处理用户 {user_id} 的3D模型生成任务，图片: {photo_object_name}，质量: {quality}")
    
    try:
        # test模式：仅模拟流程
        if quality == "test":
            return asyncio.run(simulate_generate_3d_model(self, user_id, photo_object_name))

        # 更新任务状态：开始处理
        logger.info("更新任务状态：开始处理图片")
        self.update_state(
            state='PROCESSING',
            meta={
                'status': 'processing_img',
                'message': '正在处理图片...',
                'progress': 5
            }
        )
        
        # 1. 下载图片
        file_id = os.path.splitext(os.path.basename(photo_object_name))[0]
        local_photo = f"/tmp/{os.path.basename(photo_object_name)}"
        minio_client.fget_object(config.MINIO_BUCKET, photo_object_name, local_photo)
        logger.info(f"{user_id}-{photo_object_name}图片下载完成，本地路径: {local_photo}")

        # 2. 商用API处理图片
        tpose_path = f"/tmp/{file_id}_tpose.png"
        asyncio.run(edit_image(local_photo, tpose_path, quality=quality))
        logger.info(f"{user_id}-{photo_object_name}图片处理完成，T-pose图片路径: {tpose_path}")

        # 更新任务状态：图片处理完成，开始生成3D模型
        logger.info("更新任务状态：开始生成3D模型")
        self.update_state(
            state='PROCESSING',
            meta={
                'status': 'processing_3d',
                'message': '正在生成3D模型...',
                'progress': 25
            }
        )

        # 3. 生成3D模型
        model_save_path = f"/tmp/{file_id}_model"
        asyncio.run(generate(tpose_path, model_save_path))
        model_path = model_save_path + '_base_basic_pbr.glb'
        logger.info(f"{user_id}-{photo_object_name}3D模型生成完成，模型路径: {model_path}")

        # 更新任务状态：3D模型生成完成，开始重拓扑
        self.update_state(
            state='PROCESSING',
            meta={
                'status': 'processing_remesh',
                'message': '正在重拓扑3D模型...',
                'progress': 80
            }
        )

        # 4. 重拓扑
        remesh_path = f"/tmp/{file_id}_remesh.glb"
        asyncio.run(remesh(model_path, remesh_path))
        logger.info(f"{user_id}-{photo_object_name}重拓扑完成，重拓扑模型路径: {remesh_path}")

        # 更新任务状态：重拓扑完成，开始绑定骨骼
        self.update_state(
            state='PROCESSING',
            meta={
                'status': 'processing_rigging',
                'message': '正在绑定骨骼...',
                'progress': 85
            }
        )

        # 5. 绑定骨骼
        rigged_path = f"/tmp/{file_id}_rigged.glb"
        asyncio.run(rigging(remesh_path, rigged_path))
        logger.info(f"{user_id}-{photo_object_name}骨骼绑定完成，最终模型路径: {rigged_path}")

        # 更新任务状态：骨骼绑定完成，开始上传到MinIO
        logger.info(f"{user_id}-{photo_object_name}更新任务状态：开始上传到MinIO")
        self.update_state(
            state='PROCESSING',
            meta={
                'status': 'uploading',
                'message': '正在上传最终模型...',
                'progress': 95
            }
        )

        # 6. 上传最终产物到 MinIO
        final_object_name = f"3d_models/{os.path.basename(rigged_path)}"
        minio_client.fput_object(config.MINIO_BUCKET, final_object_name, rigged_path)
        final_url = f"https://{config.MINIO_ENDPOINT}/{config.MINIO_BUCKET}/{final_object_name}"
        logger.info(f"模型上传完成，最终URL: {final_url}")

        # 获取缩略图URL（暂时从examples目录随机选择）
        examples_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'examples')
        thumbnail_files = glob.glob(os.path.join(examples_dir, '*.jpg'))
        thumbnail_path = random.choice(thumbnail_files)
        
        thumbnail_name = f"{file_id}_thumbnail.jpg"
        thumbnail_url = f"https://{config.MINIO_ENDPOINT}/{config.MINIO_BUCKET}/thumbnails/{thumbnail_name}"
        
        # 上传缩略图到 MinIO
        thumbnail_object_name = f"thumbnails/{thumbnail_name}"
        minio_client.fput_object(config.MINIO_BUCKET, thumbnail_object_name, thumbnail_path)
        logger.info(f"缩略图上传完成，路径: {thumbnail_object_name}")

        # 更新任务状态：完成
        self.update_state(
            state='SUCCESS',
            meta={
                'status': 'completed',
                'message': '处理完成',
                'progress': 100,
                'model_url': final_url,
                'thumbnail_url': thumbnail_url
            }
        )

        logger.info(f"{user_id}-{photo_object_name}3D模型生成任务成功完成")
        
        # 调用Django的回调任务更新数据库
        result_data = {
            "status": "success",
            "model_url": final_url,
            "thumbnail_url": thumbnail_url
        }
        
        # 发送回调任务到Django端
        celery_worker.send_task(
            'update_task_result',
            args=[str(self.request.id), 'SUCCESS', result_data],
            queue='callback'
        )
        
        return result_data
    except Exception as e:
        # 更新任务状态：失败
        logger.error(f"{user_id}-{photo_object_name}处理过程中发生错误: {str(e)}", exc_info=True)
        self.update_state(
            state='FAILURE',
            meta={
                'status': 'failed',
                'message': f'处理失败: {str(e)}',
                'progress': 0,
                'error': str(e),
                'exc_type': type(e).__name__,
            }
        )
        
        # 发送失败回调任务到Django端
        celery_worker.send_task(
            'update_task_result',
            args=[str(self.request.id), 'FAILURE', None, str(e)],
            queue='callback'
        )
        
        raise e

async def simulate_generate_3d_model(self, user_id, photo_object_name):
    import random
    import os
    import aiofiles
    import aiofiles.os
    import glob
    logger = logging.getLogger(__name__)
    logger.info(f"用户{user_id}进入测试模式，跳过实际处理，仅模拟流程。")
    self.update_state(
        state='PROCESSING',
        meta={
            'status': 'processing_img',
            'message': '正在处理图片...（测试模式）',
            'progress': 5
        }
    )
    await asyncio.sleep(random.uniform(0, 2))
    self.update_state(
        state='PROCESSING',
        meta={
            'status': 'processing_3d',
            'message': '正在生成3D模型...（测试模式）',
            'progress': 25
        }
    )
    await asyncio.sleep(random.uniform(0, 2))
    self.update_state(
        state='PROCESSING',
        meta={
            'status': 'processing_remesh',
            'message': '正在重拓扑3D模型...（测试模式）',
            'progress': 80
        }
    )
    await asyncio.sleep(random.uniform(0, 2))
    self.update_state(
        state='PROCESSING',
        meta={
            'status': 'processing_rigging',
            'message': '正在绑定骨骼...（测试模式）',
            'progress': 85
        }
    )
    await asyncio.sleep(random.uniform(0, 2))
    self.update_state(
        state='PROCESSING',
        meta={
            'status': 'uploading',
            'message': '正在上传最终模型...（测试模式）',
            'progress': 95
        }
    )
    
    # 获取 examples 目录中所有的 glb 文件
    examples_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'examples')
    glb_files = glob.glob(os.path.join(examples_dir, '*.glb'))
    
    if not glb_files:
        raise FileNotFoundError(f"在 {examples_dir} 目录中未找到任何 .glb 文件")
    
    # 随机选择一个 glb 文件
    selected_glb = random.choice(glb_files)
    logger.info(f"[测试模式] 随机选择的示例文件: {os.path.basename(selected_glb)}")
    
    # 获取对应的缩略图
    glb_name = os.path.splitext(os.path.basename(selected_glb))[0]
    thumbnail_path = os.path.join(examples_dir, f"{glb_name}.jpg")
    
    # 确定缩略图URL，使用 {file_id}_thumbnail.jpg 格式
    file_id = os.path.splitext(os.path.basename(photo_object_name))[0]
    thumbnail_name = f"{file_id}_thumbnail.jpg"
    thumbnail_url = f"https://{config.MINIO_ENDPOINT}/{config.MINIO_BUCKET}/thumbnails/{thumbnail_name}"
    
    # 上传缩略图到 MinIO
    thumbnail_object_name = f"thumbnails/{thumbnail_name}"
    minio_client.fput_object(config.MINIO_BUCKET, thumbnail_object_name, thumbnail_path)
    logger.info(f"[测试模式] 缩略图上传完成，路径: {thumbnail_object_name}")
    
    # 复制选中的 glb 文件到 /tmp 并重命名（异步）
    file_id = os.path.splitext(os.path.basename(photo_object_name))[0]
    rigged_path = f"/tmp/{file_id}_rigged.glb"
    
    async with aiofiles.open(selected_glb, 'rb') as src, aiofiles.open(rigged_path, 'wb') as dst:
        while True:
            chunk = await src.read(1024 * 1024)
            if not chunk:
                break
            await dst.write(chunk)
    
    final_object_name = f"3d_models/{user_id}/{os.path.basename(rigged_path)}"
    minio_client.fput_object(config.MINIO_BUCKET, final_object_name, rigged_path)
    final_url = f"{config.MINIO_ENDPOINT}/{config.MINIO_BUCKET}/{final_object_name}"
    logger.info(f"[测试模式] 模型上传完成，最终URL: {final_url}")
    self.update_state(
        state='SUCCESS',
        meta={
            'status': 'completed',
            'message': '处理完成（测试模式）',
            'progress': 100,
            'model_url': final_url,
            'thumbnail_url': thumbnail_url
        }
    )
    result_data = {
        "status": "success",
        "model_url": final_url,
        "thumbnail_url": thumbnail_url
    }
    celery_worker.send_task(
        'update_task_result',
        args=[str(self.request.id), 'SUCCESS', result_data],
        queue='callback'
    )
    
    return result_data

@shared_task(name='ssh_forward_check', queue='character_generation')
def ssh_forward_check():
    """
    定时检查并维护 SSH 转发连接
    每60秒执行一次
    """
    try:
        import subprocess
        import os
        
        # 检查环境变量
        if not os.getenv('SSH_TUNNEL_PASSWORD'):
            logger.warning("SSH_TUNNEL_PASSWORD 环境变量未设置，跳过 SSH 转发检查")
            return {"status": "skipped", "reason": "no_password"}
        
        # 执行 SSH 转发脚本
        result = subprocess.run(
            ['bash', 'app/ssh_forward.sh'],
            capture_output=True,
            text=True,
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            logger.debug("SSH 转发检查完成")
            return {"status": "success"}
        else:
            logger.warning(f"SSH 转发检查失败: {result.stderr}")
            return {"status": "failed", "error": result.stderr}
            
    except Exception as e:
        logger.error(f"SSH 转发检查异常: {str(e)}")
        return {"status": "error", "error": str(e)}

@shared_task(name='cleanup_temp_files_task', queue='character_generation')
def cleanup_temp_files_task():
    """
    定期清理 /tmp 目录中的临时文件
    每小时执行一次
    """
    try:
        import glob
        import time
        
        # 清理超过1小时的临时文件
        current_time = time.time()
        temp_patterns = [
            "/tmp/*_tpose.png",
            "/tmp/*_model*",
            "/tmp/*_remesh.glb", 
            "/tmp/*_rigged.glb",
            "/tmp/*.jpg",
            "/tmp/*.png"
        ]
        
        cleaned_count = 0
        for pattern in temp_patterns:
            files = glob.glob(pattern)
            for file_path in files:
                try:
                    # 检查文件修改时间，删除超过1小时的文件
                    if os.path.exists(file_path):
                        file_time = os.path.getmtime(file_path)
                        if current_time - file_time > 3600:  # 1小时 = 3600秒
                            if os.path.isfile(file_path):
                                os.remove(file_path)
                                cleaned_count += 1
                                logger.debug(f"定期清理删除文件: {file_path}")
                            elif os.path.isdir(file_path):
                                shutil.rmtree(file_path)
                                cleaned_count += 1
                                logger.debug(f"定期清理删除目录: {file_path}")
                except Exception as e:
                    logger.warning(f"定期清理文件失败 {file_path}: {str(e)}")
        
        logger.info(f"定期清理完成，删除了 {cleaned_count} 个临时文件/目录")
        return {"status": "success", "cleaned_count": cleaned_count}
        
    except Exception as e:
        logger.error(f"定期清理任务异常: {str(e)}")
        return {"status": "error", "error": str(e)}