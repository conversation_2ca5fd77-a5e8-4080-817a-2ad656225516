# Mokta Backend

一个基于 Django 的后端系统，用于通过 OAuth 2.0 认证实现 2D 照片到 3D 角色的生成。

## 🚀 快速开始

```bash
# 1. 环境设置
./setup.sh

# 2. 启动服务（Django + Celery Worker）
./entrypoint.sh dev        # 开发环境
./entrypoint.sh production # 生产环境

# 3. 访问服务
# API: http://localhost:8080
# 管理后台: http://localhost:8080/admin/ (仅管理员用户)
# 测试页面: frontend-test/index.html
# Django日志: logs/django.log
# Worker日志: logs/celery-worker.log

# 4. 创建管理员账户 (可选)
python manage.py createsuperuser
```

## 📁 项目结构

```
mokta-backend/
├── setup.sh                    # 环境设置脚本（Python 3.11 + uv）
├── entrypoint.sh               # 服务启动脚本
├── pyproject.toml              # Python 3.11 依赖配置
├── .env.production             # 生产环境配置
├── .env.dev                    # 开发环境配置
├── .env                        # 当前环境链接（自动创建）
├── manage.py                   # Django 管理脚本（内置环境变量加载）
├── mokta/                      # 主 Django 项目
│   ├── settings.py             # Django 配置（支持环境变量）
│   ├── urls.py                 # 主 URL 路由
│   ├── wsgi.py                 # WSGI 配置
│   ├── asgi.py                 # ASGI 配置
│   └── celery.py               # Celery 配置
├── apps/                       # Django 应用（分层架构）
│   ├── authentication/        # 🔐 OAuth 认证（Google/Apple）[基础设施]
│   ├── users/                  # 👤 用户管理（自定义 User 模型）[基础设施]
│   ├── uploads/                # 📤 文件上传（预签名 URL）[基础设施]  
│   ├── tasks/                  # ⚙️ 异步任务管理（Celery状态查询）[基础设施]
│   ├── characters/             # 🎭 3D 角色生成业务逻辑 [业务层]
│   └── motions/                # 🎭 3D 动作下载业务逻辑 [业务层]
├── services/                   # 业务逻辑服务
│   ├── storage_service.py      # 对象存储服务（MinIO/S3）
│   └── external_apis/          # 外部API服务集成
├── celery_worker.py            # GPU服务器端任务定义（参考用）
├── frontend-test/              # 前端测试页面
│   ├── index.html              # 完整 API 测试页面
│   ├── simple-test.html        # 简化测试页面
│   └── start-test-server.py    # 测试服务器启动脚本
└── docs/                       # 项目文档
    ├── architecture.md         # 系统架构文档
    ├── api.md                  # API 接口文档
    ├── celery.md               # Celery 集成规范
    ├── common_bugs.md          # 常见问题解决方案
    └── deployment.md           # 部署指南
```

## 📚 文档

- **[系统架构](docs/architecture.md)** - 详细的系统设计和组件说明
- **[API 文档](docs/api.md)** - 完整的 API 接口参考
- **[Celery集成](docs/celery.md)** - Django与GPU服务器的Celery集成规范
- **[常见问题](docs/common_bugs.md)** - 开发过程中的常见问题解决方案
- **[部署指南](docs/deployment.md)** - 生产环境部署指南
- **[前端测试](frontend-test/README.md)** - API 测试页面使用说明

## 🏗️ 架构设计理念

本项目采用**分层架构**和**单一职责原则**，将代码组织为基础设施层和业务逻辑层：

### 📦 **分层结构**

```
┌─────────────────────────────────────┐
│           业务逻辑层                 │
│  ┌─────────────┐  ┌─────────────┐    │
│  │ characters/ │  │   images/   │    │  ← 业务专用功能
│  │角色生成业务  │  │图片生成业务   │    │
│  └─────────────┘  └─────────────┘    │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│           基础设施层                 │
│  ┌─────────────┐  ┌─────────────┐    │
│  │  uploads/   │  │   tasks/    │    │  ← 通用技术组件
│  │文件存储管理  │  │异步任务管理   │    │
│  └─────────────┘  └─────────────┘    │
└─────────────────────────────────────┘
```

### 🔧 **App 职责分工**

| App | 职责 | 特点 | 复用性 |
|-----|------|------|--------|
| **`uploads/`** | 文件存储管理 | 预签名URL、文件验证、对象存储抽象 | ✅ 高复用 |
| **`tasks/`** | 异步任务管理 | Celery任务状态查询、进度跟踪 | ✅ 高复用 |
| **`characters/`** | 角色生成业务 | 3D角色生成逻辑、业务参数验证 | ❌ 业务专用 |
| **`motions/`** | 动作下载业务 | 3D动作下载逻辑、动作资源管理 | ❌ 业务专用 |
| **`authentication/`** | 用户认证 | OAuth登录、JWT管理 | ✅ 高复用 |
| **`users/`** | 用户管理 | 用户信息、权限管理 | ✅ 高复用 |

## 🛠️ 技术栈

- **后端**: Django 5.2 + DRF + Celery 5.3+
- **数据库**: PostgreSQL + Redis 5.0+
- **存储**: MinIO 7.1+ / S3 (boto3)
- **认证**: OAuth 2.0 (Google/Apple) + JWT 2.8+
- **图像处理**: Pillow 10.1+
- **工具**: Python 3.11 + uv + Gunicorn 21.2+

## ✨ 核心功能

### 🔐 用户认证与授权
- OAuth 2.0 登录（Google/Apple）
- JWT Token 管理
- 用户每日生成限额控制

### 📤 文件上传与存储  
- 预签名 URL 安全上传
- 多种存储后端支持（MinIO/S3）
- 文件类型和大小验证

### 🎭 3D 角色生成
- 支持多种生成质量：`high`、`medium`、`low`、`flux`、`test`
- 异步任务处理（Django端）+ GPU计算（独立服务器）
- 实时进度跟踪和状态查询
- 任务失败重试机制

### ⚙️ 分布式任务系统
- **Django端**: 任务创建、状态查询、数据库管理
- **GPU服务器端**: 实际的3D生成计算
- **回调机制**: GPU任务完成后自动更新Django数据库
- **队列隔离**: `character_generation`队列（GPU任务），`callback`队列（回调任务）

### 🔄 系统集成
- Django服务器自动启动Celery Worker接收回调任务
- 优雅的进程管理和信号处理
- 统一的日志记录和错误跟踪